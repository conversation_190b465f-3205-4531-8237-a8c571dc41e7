import { Dropdown, DropdownItem } from "@choiceform/ui-react";
import { EditorState, StateField } from "@codemirror/state";
import { Tooltip, showTooltip } from "@codemirror/view";
import { createRoot } from "react-dom/client";

function getCursorTooltips(state: EditorState) {
  return state.selection.ranges
    .filter((range) => {
      return range.empty;
    })
    .map((range) => {
      const line = state.doc.lineAt(range.head);
      const text = `${line.number}:${range.head - line.from}`;
      return {
        pos: range.head,
        above: true,
        strictSide: true,
        arrow: true,
        create: () => {
          const dom = document.createElement("div");
          const root = createRoot(dom);
          root.render(
            <Dropdown>
              {[
                { id: "1", label: 1 },
                { id: "2", label: 2 },
              ].map((item) => (
                <DropdownItem key={item.id}>{item.label}</DropdownItem>
              ))}
            </Dropdown>,
          );
          dom.textContent = text;
          return { dom };
        },
      };
    });
}

export const menuTooltipField = StateField.define<readonly Tooltip[]>({
  create: getCursorTooltips,

  update(tooltips, tr) {
    if (!tr.docChanged && !tr.selection) return tooltips;
    return getCursorTooltips(tr.state);
  },

  provide: (f) => showTooltip.computeN([f], (state) => state.field(f)),
});
