import { useEffect, useMemo, useRef } from "react";
import * as d3 from "d3";
import { useMaxLabelWidth } from "./hooks/use-label-width";
import { Axis } from "./common/axis";
import { Grid } from "./common/grid";
import { useRotatedDimensions } from "./hooks/use-rotated-dimensions";
import {
  axisAtom,
  barBackgroundAtom,
  barBackgroundStyleAtom,
  barCornerRadiusAtom,
  barLabelAtom,
  barPaddingAtom,
  barStyleAtom,
  calculateMeanAtom,
  calculateMedianAtom,
  gridAtom,
  gridStyleAtom,
  marginAtom,
  orientationAtom,
  scaleRangeAtom,
  tickArgumentsAtom,
  ticksAtom,
  tickSizeAtom,
  ticksFormatAtom,
  ticksLabelFormatAtom,
  ticksStyleAtom,
  barGradientAtom,
  specialColorMapAtom,
  barGradientOrientationAtom,
  sizeAtom,
  calculateModeStyleAtom,
  calculateMedianStyleAtom,
  dataSortAtom,
  ticksAttachment<PERSON><PERSON>,
  animationState<PERSON>tom,
  histogramDataAtom,
  thresholds<PERSON>tom,
  thresholds<PERSON>um<PERSON><PERSON>,
  axis<PERSON>abel<PERSON>tom,
} from "./atom";
import { useAtom, useAtomValue } from "jotai";
import { v4 as uuidv4 } from "uuid";
import linearGradient from "./common/linear-gradient";
import { useCalculateMargins } from "./hooks/use-calculate-margin";

export interface HistogramChartProps {
  id: string;
}

export const HistogramChart = ({ id }: HistogramChartProps) => {
  const ref = useRef(null);
  const data = useAtomValue(histogramDataAtom(id));
  const dataSort = useAtomValue(dataSortAtom(id));

  const size = useAtomValue(sizeAtom(id));
  const orientation = useAtomValue(orientationAtom(id));
  const margin = useAtomValue(marginAtom(id));
  const axis = useAtomValue(axisAtom(id));
  const axisLabel = useAtomValue(axisLabelAtom(id));
  const tickArguments = useAtomValue(tickArgumentsAtom(id));
  const ticksStyle = useAtomValue(ticksStyleAtom(id));
  const scaleRange = useAtomValue(scaleRangeAtom(id));
  const ticks = useAtomValue(ticksAtom(id));
  const tickSize = useAtomValue(tickSizeAtom(id));
  const ticksFormat = useAtomValue(ticksFormatAtom(id));
  const ticksLabelFormat = useAtomValue(ticksLabelFormatAtom(id));
  const barPadding = useAtomValue(barPaddingAtom(id));
  const barCornerRadius = useAtomValue(barCornerRadiusAtom(id));
  const barGradient = useAtomValue(barGradientAtom(id));
  const barGradientOrientation = useAtomValue(barGradientOrientationAtom(id));
  const specialColorMap = useAtomValue(specialColorMapAtom(id));
  const barStyle = useAtomValue(barStyleAtom(id));
  const barBackground = useAtomValue(barBackgroundAtom(id));
  const barBackgroundStyle = useAtomValue(barBackgroundStyleAtom(id));
  const barLabel = useAtomValue(barLabelAtom(id));
  const grid = useAtomValue(gridAtom(id));
  const gridStyle = useAtomValue(gridStyleAtom(id));
  const calculateMean = useAtomValue(calculateMeanAtom(id));
  const calculateModeStyle = useAtomValue(calculateModeStyleAtom(id));
  const calculateMedian = useAtomValue(calculateMedianAtom(id));
  const calculateMedianStyle = useAtomValue(calculateMedianStyleAtom(id));
  const [animationState, setAnimationState] = useAtom(animationStateAtom(id));
  const ticksAttachment = useAtomValue(ticksAttachmentAtom(id));
  const thresholds = useAtomValue(thresholdsAtom(id));
  const thresholdsNum = useAtomValue(thresholdsNumAtom(id));

  const width = size.width;
  const height = size.height;

  // 提取标签
  const calculateHorizontalLabels = data.map((_, index) => index);
  const calculateVerticalLabels = data.map((_, index) => index);

  const { marginTop, marginRight, marginBottom, marginLeft, maxBottomLabelsWidth } =
    useCalculateMargins({
      ref,
      ticks,
      ticksStyle,
      tickSize,
      axis,
      axisLabel,
      margin,
      useMaxLabelWidth,
      useRotatedDimensions,
      ticksAttachment,
      calculateHorizontalLabels,
      calculateVerticalLabels,
    });

  // 插值器渐变
  const gradient = useMemo(() => {
    return barStyle.gradient ?? ["#000", "#888", "#fff"];
  }, [barStyle.gradient]);

  const colorInterpolator = d3.interpolateRgbBasis(gradient);

  useEffect(() => {
    if (!ref.current) return;
    const svg = d3
      .select(ref.current)
      .attr("width", width + marginLeft + marginRight)
      .attr("height", height + marginTop + marginBottom);

    svg.selectAll("*").remove();

    // 线性渐变
    const linearGradientId = uuidv4();
    linearGradient(svg, linearGradientId, gradient, barGradientOrientation);

    if (orientation === "vertical") {
      // Create scales
      const xScale = d3
        .scaleLinear()
        .domain(scaleRange ?? [d3.min(data, (d) => d)!, d3.max(data, (d) => d)!])
        .range([0, width]);

      const yScale = d3.scaleLinear().range([height, 0]);

      // Generate histogram data
      const histogram = d3
        .bin()
        .value(function (d) {
          return d;
        })
        .domain([d3.min(xScale.domain()) || 0, d3.max(xScale.domain()) || 0]);

      switch (thresholds) {
        case "sturges":
          histogram.thresholds(d3.thresholdSturges(data));
          break;
        case "scott":
          histogram.thresholds(d3.thresholdScott(data, d3.min(data) || 0, d3.max(data) || 0));
          break;
        case "freedmanDiaconis":
          histogram.thresholds(
            d3.thresholdFreedmanDiaconis(data, d3.min(data) || 0, d3.max(data) || 0),
          );
          break;
        case "binNum":
          histogram.thresholds(xScale.ticks(thresholdsNum));
          break;
      }

      const bins = histogram(data);

      yScale.domain([0, d3.max(bins, (d) => d.length)!]);

      Grid(
        svg,
        xScale,
        yScale,
        tickArguments,
        width,
        height,
        marginLeft,
        marginTop,
        animationState,
        grid,
        gridStyle,
      );

      if (barBackground) {
        const background = svg
          .append("g")
          .attr("class", "background")
          .attr("transform", `translate(${marginLeft},${marginTop})`);
        background
          .selectAll(".background")
          .data(bins)
          .join("rect")
          .attr("x", (d) => xScale(d.x0!) ?? 0)
          .attr("y", 0)
          .attr("width", (d) => xScale(d.x1!) - xScale(d.x0!) - 1)
          .attr("height", height)
          .attr("fill", barBackgroundStyle?.fill ?? "none")
          .attr("fill-opacity", barBackgroundStyle?.fillOpacity ?? 0.1)
          .attr("stroke", barBackgroundStyle?.stroke ?? "none")
          .attr("stroke-width", barBackgroundStyle?.strokeWidth ?? 1);
        if (animationState.animation) {
          const transition = d3
            .select<SVGRectElement, [string, number]>(".background")
            .attr("opacity", 0)
            .transition()
            .delay(animationState.delay)
            .duration(animationState.duration)
            .attr("opacity", 1);
          transition.attr("opacity", 1);
        } else {
          background.selectAll(".background").attr("opacity", 1);
        }
      }

      // Add bars
      const bars = svg
        .append("g")
        .attr("class", "bars")
        .attr("transform", `translate(${marginLeft},${marginTop})`);

      bars
        .selectAll(".bar")
        .data(bins)
        .join("rect")
        .attr("width", (d) => {
          const x1 = xScale(d.x1!);
          const x0 = xScale(d.x0!);
          if (x1 === undefined || x0 === undefined) {
            return 0;
          }
          const width = x1 - x0 - 1;
          return isNaN(width) ? 0 : Math.max(0, width);
        })

        .attr("fill", function (d) {
          if (d[0] in specialColorMap) {
            return specialColorMap[d[0]];
          }
          if (barGradient === "linear") {
            return `url(#${linearGradientId})`;
          }
          if (barGradient === "interpolation") {
            if (d[0] < thresholdsNum) {
              return gradient[0];
            } else {
              return gradient[1];
            }
          }
          return barStyle?.fill ?? "black";
        })
        .attr("fill-opacity", barStyle?.fillOpacity ?? 1)
        .attr("stroke", barStyle?.stroke ?? "none")
        .attr("stroke-width", barStyle?.strokeWidth ?? 1)
        .each(function (d) {
          const self = d3.select(this as SVGRectElement);
          if (animationState.animation) {
            self
              .attr("x", (d: any) => {
                const x = xScale(d.x0!);
                return isNaN(x) ? 0 : x;
              })
              .attr("y", height)
              .attr("height", 0)
              .transition()
              .duration(animationState.duration)
              .attr("y", yScale(d.length) ?? 0)
              .attr("height", (d: any) => height - yScale(d.length))
              .on("end", () => {
                setAnimationState((prev) => ({
                  ...prev,
                  animation: false,
                }));
              });
          } else {
            self
              .attr("x", xScale(d.x0!) ?? 0)
              .attr("y", yScale(d.length) ?? 0)
              .attr("height", (d: any) => height - yScale(d.length));
          }
        });

      // 计算数据的平均值和标准差
      const mean: number = d3.mean(data) as number;
      const stdDev: number = d3.deviation(data) as number;

      // 创建一个正态分布函数
      const normalDistribution = (x: number): number => {
        return (
          (1 / (stdDev * Math.sqrt(2 * Math.PI))) *
          Math.exp(-Math.pow(x - mean, 2) / (2 * Math.pow(stdDev, 2)))
        );
      };

      // 计算正态分布曲线的最大概率密度
      const maxDensity = normalDistribution(mean);

      // 创建一个新的y轴比例尺，用于绘制正态分布曲线
      const yScaleForLine = d3.scaleLinear().domain([0, maxDensity]).range([height, 0]);

      // 创建一个线生成器
      const line = d3
        .line<{ x: number; y: number }>()
        .defined((d) => !isNaN(d.y))
        .x((d) => xScale(d.x))
        .y((d) => yScaleForLine(d.y));

      const normalData = xScale.ticks(500).map((x) => ({ x, y: normalDistribution(x) }));

      // 在SVG中添加正态分布曲线
      bars
        .append("path")
        .datum(normalData) // 使用创建的数据集作为线生成器的输入数据
        .attr("fill", "none")
        .attr("stroke", "red")
        .attr("stroke-width", 1.5)
        .attr("d", line);

      Axis(
        svg,
        xScale,
        yScale,
        "vertical",
        marginLeft,
        marginTop,
        marginRight,
        marginBottom,
        width,
        height,
        animationState,
        axis,
        axisLabel,
        ticks,
        tickSize,
        tickArguments,
        ticksStyle,
        ticksLabelFormat,
        ticksFormat,
        ticksAttachment,
      );
    } else {
      // Create scales
      const yScale = d3
        .scaleLinear()
        .domain([d3.min(data)!, d3.max(data)!])
        .range([height, 0]);
      const xScale = d3.scaleLinear().range([0, width]);

      // Generate histogram data
      const bins = d3.bin().thresholds(yScale.ticks(100))(data);

      xScale.domain([0, d3.max(bins, (d) => d.length)!]);

      Grid(
        svg,
        xScale,
        yScale,
        tickArguments,
        width,
        height,
        marginLeft,
        marginTop,
        animationState,
        grid,
        gridStyle,
      );

      if (barBackground) {
        const background = svg
          .append("g")
          .attr("class", "background")
          .attr("transform", `translate(${marginLeft},${marginTop})`);

        background
          .selectAll(".background")
          .data(bins)
          .join("rect")
          .attr("y", (d) => yScale(d.x1!))
          .attr("x", 0)
          .attr("height", (d) => yScale(d.x0!) - yScale(d.x1!) - 1)
          .attr("width", width)
          .attr("fill", barBackgroundStyle?.fill ?? "none")
          .attr("fill-opacity", barBackgroundStyle?.fillOpacity ?? 0.1)
          .attr("stroke", barBackgroundStyle?.stroke ?? "none")
          .attr("stroke-width", barBackgroundStyle?.strokeWidth ?? 1);
        if (animationState.animation) {
          const transition = d3
            .select<SVGRectElement, [string, number]>(".background")
            .attr("opacity", 0)
            .transition()
            .delay(animationState.delay)
            .duration(animationState.duration)
            .attr("opacity", 1);
          transition.attr("opacity", 1);
        } else {
          background.selectAll(".background").attr("opacity", 1);
        }
      }

      // Add bars
      const bars = svg
        .append("g")
        .attr("class", "bar")
        .attr("transform", `translate(${marginLeft},${marginTop})`);

      bars
        .selectAll(".bar")
        .data(bins)
        .join("rect")
        .attr("height", (d) => yScale(d.x0!) - yScale(d.x1!) - 1)
        .attr("fill", function (d) {
          if (d[0] in specialColorMap) {
            return specialColorMap[d[0]];
          }
          if (barGradient === "linear") {
            return `url(#${linearGradientId})`;
          }
          if (barGradient === "interpolation") {
            if (d[0] < 20) {
              return "orange";
            } else {
              return "#69b3a2";
            }
          }
          return barStyle?.fill ?? "black";
        })
        .attr("fill-opacity", barStyle?.fillOpacity ?? 1)
        .attr("stroke", barStyle?.stroke ?? "none")
        .attr("stroke-width", barStyle?.strokeWidth ?? 1)
        .each(function (d) {
          const self = d3.select(this as SVGRectElement);
          if (animationState.animation) {
            self
              .attr("y", yScale(d.x1!))
              .attr("x", 0)
              .attr("width", 0)
              .transition()
              .duration(animationState.duration)
              .attr("x", 0)
              .attr("width", (d: any) => xScale(d.length))
              .on("end", () => {
                setAnimationState((prev) => ({
                  ...prev,
                  animation: false,
                }));
              });
          } else {
            self
              .attr("y", yScale(d.x1!))
              .attr("x", 0)
              .attr("width", (d: any) => xScale(d.length));
          }
        });

      // Add axes
      Axis(
        svg,
        xScale,
        yScale,
        "horizontal",
        marginLeft,
        marginTop,
        marginRight,
        marginBottom,
        width,
        height,
        animationState,
        axis,
        axisLabel,
        ticks,
        tickSize,
        tickArguments,
        ticksStyle,
        ticksLabelFormat,
        ticksFormat,
        ticksAttachment,
      );
    }
  }, [
    ticksAttachment,
    axis,
    barBackground,
    barBackgroundStyle,
    barCornerRadius,
    barGradient,
    barGradientOrientation,
    barLabel,
    barPadding,
    barStyle?.fill,
    barStyle?.fillOpacity,
    barStyle?.stroke,
    barStyle?.strokeWidth,
    calculateMean,
    calculateMedian,
    calculateMedianStyle,
    calculateModeStyle,
    colorInterpolator,
    data,
    dataSort.name,
    dataSort.value,
    gradient,
    grid,
    gridStyle,
    height,
    marginBottom,
    marginLeft,
    marginRight,
    marginTop,
    maxBottomLabelsWidth,
    orientation,
    scaleRange,
    specialColorMap,
    tickArguments,
    tickSize,
    ticks,
    ticksFormat,
    ticksLabelFormat,
    ticksStyle,
    width,
    animationState,
    setAnimationState,
    thresholds,
    thresholdsNum,
    axisLabel,
  ]);

  return (
    <svg
      style={{
        overflow: "visible",
      }}
      ref={ref}
    />
  );
};
