import { FeaElementClasses } from "@choiceform/ui-react";
import { VariantProps, tv } from "tailwind-variants";

const CodeInputVariant = tv({
  slots: {
    base: "flex flex-col min-w-0",
    inputWrapper: "flex flex-col overflow-hidden",
    scrollArea: "inline-flex min-w-0 min-h-inherit rounded-inherit",
    scrollAreaViewport: "max-h-96 min-h-inherit",
    scrollAreaContent: [
      "group cursor-text relative flex flex-row",
      "max-w-full w-full min-h-inherit",
    ],
    indicator: "pointer-events-none flex-shrink-0 self-stretch flex",
    indicatorContainer: "sticky items-start",
    indicatorButton:
      "pointer-events-auto flex flex-shrink-0 transition-colors flex-center cursor-pointer",
    indicatorIcon: "",
    codeMirror: "flex-grow min-w-0",
    ...FeaElementClasses,
  },
  variants: {
    setSize: {
      none: "",
      xs: {
        base: "gap-0.5",
        inputWrapper: "min-h-5.5 rounded-xs text-xs",
        indicator: "px-0.5",
        indicatorButton: "w-4.5 h-4.5 rounded-xxs",
        indicatorIcon: "w-3 h-3",
      },
      sm: {
        base: "gap-0.75",
        inputWrapper: "rounded-sm text-sm",
        indicator: "px-1",
        indicatorButton: "w-5.5 h-5.5 rounded-xs",
        indicatorIcon: "w-3.5 h-3.5",
      },
      md: {
        base: "gap-1",
        inputWrapper: "min-h-9.5 rounded",
        indicator: "px-1.5",
        indicatorButton: "w-6.5 h-6.5 rounded-sm",
        indicatorIcon: "w-4 h-4",
      },
      lg: {
        base: "gap-1.75",
        inputWrapper: "min-h-11.5 rounded-lg text-lg",
        indicator: "px-2",
        indicatorButton: "w-7.5 h-7.5 rounded",
        indicatorIcon: "w-4.5 h-4.5",
      },
    },
    editorType: {
      editor: {},
      input: {
        scrollAreaContent: "items-center",
      },
      formula: {},
    },
    isDisabled: {
      false: {},
      true: {
        indicatorButton: "text-secondary cursor-not-allowed",
      },
    },
    isOpen: {
      true: {},
      false: {},
    },
  },
  compoundVariants: [
    {
      editorType: ["input", "formula"],
      setSize: "xs",
      class: { scrollAreaContent: "pl-1.5 py-0.5 gap-0.5" },
    },
    {
      editorType: ["input", "formula"],
      setSize: "sm",
      class: { scrollAreaContent: "pl-2.25 py-1 gap-1" },
    },
    {
      editorType: ["input", "formula"],
      setSize: "md",
      class: { scrollAreaContent: "pl-3 py-1.5 gap-1.5" },
    },
    {
      editorType: ["input", "formula"],
      setSize: "lg",
      class: { scrollAreaContent: "pl-3.75 py-2 gap-2" },
    },
    {
      isDisabled: false,
      isOpen: true,
      class: { indicatorButton: "bg-fade-100" },
    },
    {
      isDisabled: false,
      isOpen: false,
      class: { indicatorButton: "hover:bg-fade-100" },
    },
  ],
  defaultVariants: {
    setSize: "md",
    isDisabled: false,
  },
});

export type CodeInputVariantProps = VariantProps<typeof CodeInputVariant>;
export type CodeInputSlots = keyof ReturnType<typeof CodeInputVariant>;
export { CodeInputVariant };
