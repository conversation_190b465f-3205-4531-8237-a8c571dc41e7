import {
  Decoration,
  MatchDecorator,
  WidgetType,
  DecorationSet,
  EditorView,
  ViewPlugin,
  ViewUpdate,
} from "@codemirror/view";
import { createRoot } from "react-dom/client";
import { CodeInputProps } from "../types";

export const placeholders = function (quoteRender: { current: CodeInputProps["quoteRender"] }) {
  class PlaceholderWidget extends WidgetType {
    readonly label: string;
    index: number;
    dom?: HTMLElement;
    constructor(label: string, index: number) {
      super();
      this.label = label;
      this.index = index;
    }

    changeDom(index: number) {
      if (index === 0) {
        if (this.dom?.previousSibling) {
          (this.dom.previousSibling as HTMLElement).style.marginLeft = "-4px";
        }
      } else {
        if (this.dom?.previousSibling) {
          (this.dom.previousSibling as HTMLElement).style.marginLeft = "";
        }
      }
    }

    eq(other: PlaceholderWidget) {
      setTimeout(() => this.changeDom(other.index), 0);
      return other.label === this.label;
    }

    toDOM() {
      if (quoteRender.current) {
        let data;
        try {
          data = JSON.parse(this.label);
        } catch (error) {
          data = this.label;
        }
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const rem = quoteRender.current(data) as any;
        if (rem["$$typeof"]) {
          const dom = document.createElement("span");
          const root = createRoot(dom);
          root.render(rem);
          this.dom = dom;
          setTimeout(() => this.changeDom(this.index), 0);
          return dom;
        } else {
          this.dom = rem;
          setTimeout(() => this.changeDom(this.index), 0);
          return rem;
        }
      }
      // dom.className = 'cm-default-quote';
      // try {
      //   const data = JSON.parse(this.label) as any;
      //   if (data.name) {
      //     dom.innerHTML = data.name;
      //   } else {
      //     dom.innerHTML = data.toSring();
      //   }
      // } catch (error) {
      //   dom.innerHTML = this.label;
      // }
      // return dom;
    }
  }

  const placeholderMatcher = new MatchDecorator({
    regexp: /\$\{\{(.*?)\}\}\$/g,
    // regexp: /\{\{(\w+)\s/g,
    decoration: (match) => {
      const widget = new PlaceholderWidget(match[1], match.index);
      return Decoration.replace({
        widget,
      });
    },
  });

  return ViewPlugin.fromClass(
    class {
      placeholders: DecorationSet;
      constructor(view: EditorView) {
        this.placeholders = placeholderMatcher.createDeco(view);
      }
      update(update: ViewUpdate) {
        this.placeholders = placeholderMatcher.updateDeco(update, this.placeholders);
      }
    },
    {
      decorations: (instance) => instance.placeholders,
      provide: (plugin) =>
        EditorView.atomicRanges.of((view) => {
          return view.plugin(plugin)?.placeholders || Decoration.none;
        }),
    },
  );
};
