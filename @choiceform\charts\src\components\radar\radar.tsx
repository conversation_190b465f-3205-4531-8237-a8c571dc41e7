import { useEffect, useRef } from "react";
import * as d3 from "d3";

type RadarData = {
  axis: string;
  value: number;
};

type radarStyle = {
  fill?: string[];
  fillOpacity?: number[];
  stroke?: string[];
  strokeWidth?: number;
  strokeOpacity?: number;
};

type radarGridStyle = {
  stroke?: string;
  strokeWidth?: number;
  strokeOpacity?: number;
  fill?: string;
  fillOpacity?: number;
};

type RadarAxisStyle = {
  fill?: string;
  fillOpacity?: number;
  stroke?: string;
  strokeWidth?: number;
  strokeOpacity?: number;
  fontSize?: number;
};

interface RadarChartProps {
  width: number;
  height: number;
  data: RadarData[][];
  gridCircleCount?: number;
  areaStyle?: radarStyle;
  dotStyle?: radarStyle;
  gridStyle?: {
    grid?: radarGridStyle;
    axis?: radarGridStyle;
    text?: RadarAxisStyle;
  };
  curve?: boolean;
  margin?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

export const RadarChart = ({
  width,
  height,
  data,
  gridCircleCount = 5,
  areaStyle,
  dotStyle,
  gridStyle,
  curve = false,
  margin,
}: RadarChartProps) => {
  const chartRef = useRef(null);

  const marginTop = margin?.top ?? 50;
  const marginRight = margin?.right ?? 80;
  const marginBottom = margin?.bottom ?? 50;
  const marginLeft = margin?.left ?? 80;

  useEffect(() => {
    const svg = d3
      .select(chartRef.current)
      .attr("width", width + marginLeft + marginRight)
      .attr("height", height + marginTop + marginBottom);

    svg.selectAll("*").remove();

    const axis = data[0].map((d) => d.axis);
    const radius = Math.min(width, height) / 2;
    const maxValue = d3.max(data, (d) => d3.max(d, (i) => i.value)) ?? 0;
    const angleSlice = (Math.PI * 2) / axis.length;
    const tickValues = d3.range(0, maxValue, maxValue / gridCircleCount).concat([maxValue]);

    const rScale = d3
      .scaleLinear()
      .domain([0, maxValue ?? 0])
      .range([0, radius]);

    const radarGrid = svg
      .append("g")
      .attr("transform", `translate(${width / 2 + marginLeft}, ${height / 2 + marginTop})`);

    radarGrid
      .selectAll(".gridCircle")
      .data(d3.range(1, gridCircleCount + 1))
      .enter()
      .append("circle")
      .attr("cx", 0)
      .attr("cy", 0)
      .attr("r", (d) => (radius / gridCircleCount) * d)
      .attr("fill", gridStyle?.grid?.fill ?? "#CDCDCD")
      .attr("fill-opacity", gridStyle?.grid?.fillOpacity ?? 0.1)
      .attr("stroke", gridStyle?.grid?.stroke ?? "#CDCDCD")
      .attr("stroke-width", gridStyle?.grid?.strokeWidth ?? 1)
      .attr("stroke-opacity", gridStyle?.grid?.strokeOpacity ?? 1);

    const axisGridLine = radarGrid
      .append("g")
      .attr("class", "axisGridLines")
      .attr("stroke", gridStyle?.axis?.stroke ?? "white")
      .attr("stroke-width", gridStyle?.axis?.strokeWidth ?? 2)
      .attr("stroke-opacity", gridStyle?.axis?.strokeOpacity ?? 1);

    const axisGridText = radarGrid
      .append("g")
      .attr("class", "axisGridText")
      .style("font-size", gridStyle?.text?.fontSize ?? 12)
      .style("fill", gridStyle?.text?.fill ?? "currentColor")
      .style("stroke", gridStyle?.text?.stroke ?? "white")
      .style("stroke-width", gridStyle?.text?.strokeWidth ?? 3)
      .style("paint-order", "stroke")
      .style("text-anchor", "middle")
      .style("stroke-linejoin", "round");

    axisGridLine
      .selectAll(".axisGridLines")
      .data(axis)
      .enter()
      .append("line")
      .attr("x1", 0)
      .attr("y1", 0)
      .attr("x2", (_, i) => rScale(maxValue ?? 0) * Math.cos(angleSlice * i - Math.PI / 2))
      .attr("y2", (_, i) => rScale(maxValue ?? 0) * Math.sin(angleSlice * i - Math.PI / 2));

    axisGridText
      .selectAll(".axisGridText")
      .data(axis)
      .enter()
      .append("text")
      .attr("dy", "0.35em")
      .attr("x", (_, i) => rScale((maxValue ?? 0) * 1.1) * Math.cos(angleSlice * i - Math.PI / 2))
      .attr("y", (_, i) => rScale((maxValue ?? 0) * 1.1) * Math.sin(angleSlice * i - Math.PI / 2))
      .text((d) => d);

    const tick = svg
      .append("g")
      .attr("transform", `translate(${width / 2 + marginLeft}, ${height / 2 + marginTop})`)
      .style("font-size", 12)
      .style("fill", "currentColor")
      .style("stroke", "white")
      .style("stroke-width", 3)
      .style("paint-order", "stroke")
      .style("text-anchor", "start")
      .style("stroke-linejoin", "round");

    // 添加刻度标签
    tick
      .selectAll(".tickLabel")
      .data(tickValues)
      .enter()
      .append("text")
      .attr("class", "tickLabel")
      .attr("x", 4) // 调整x坐标来定位刻度标签
      .attr("y", (d) => -rScale(d)) // 调整y坐标来定位刻度标签
      .attr("dy", "0.35em") // 使文字垂直居中
      .text((d) => d.toFixed(2)); // 使用刻度数值作为标签，保留两位小数

    const radarLine = d3
      .lineRadial<RadarData>()
      .radius((d) => rScale(d.value))
      .angle((_, i) => i * angleSlice)
      .curve(curve ? d3.curveCardinalClosed : d3.curveLinearClosed);

    const radarArea = svg
      .append("g")
      .attr("transform", `translate(${width / 2 + marginLeft}, ${height / 2 + marginTop})`);

    data.forEach((dataGroup, dataIndex) => {
      const radarCircle = radarArea.append("g").attr("class", "radarCircle");

      radarArea
        .append("path")
        .attr("class", "radarArea")
        .attr("d", radarLine(dataGroup))
        .attr(
          "fill",
          areaStyle?.fill
            ? areaStyle?.fill[dataIndex % areaStyle?.fill.length]
            : d3.schemeCategory10[dataIndex % 10],
        )
        .attr(
          "fill-opacity",
          areaStyle?.fillOpacity
            ? areaStyle?.fillOpacity[dataIndex % areaStyle?.fillOpacity.length]
            : 0.25,
        )
        .attr(
          "stroke",
          areaStyle?.stroke
            ? areaStyle?.stroke[dataIndex % areaStyle?.stroke.length]
            : d3.schemeCategory10[dataIndex % 10],
        )
        .attr("stroke-width", areaStyle?.strokeWidth ?? 1)
        .attr("stroke-opacity", areaStyle?.strokeOpacity ?? 1)
        .on("mouseover", function () {
          d3.select(this).transition().duration(200).style("fill-opacity", 0.7);
        })
        .on("mouseout", function () {
          d3.select(this).transition().duration(200).style("fill-opacity", 0.25);
        });

      radarCircle
        .selectAll(".radarCircle")
        .data(dataGroup)
        .enter()
        .append("circle")
        .attr("class", "radarCircle")
        .attr("r", 4)
        .attr("cx", (d, i) => rScale(d.value) * Math.cos(angleSlice * i - Math.PI / 2))
        .attr("cy", (d, i) => rScale(d.value) * Math.sin(angleSlice * i - Math.PI / 2))
        .attr(
          "fill",
          dotStyle?.fill
            ? dotStyle?.fill[dataIndex % dotStyle?.fill.length]
            : d3.schemeCategory10[dataIndex % 10],
        )
        .attr(
          "fill-opacity",
          dotStyle?.fillOpacity
            ? dotStyle?.fillOpacity[dataIndex % dotStyle?.fillOpacity.length]
            : 0.25,
        )
        .attr(
          "stroke",
          dotStyle?.stroke ? dotStyle?.stroke[dataIndex % dotStyle?.stroke.length] : "white",
        )
        .attr("stroke-width", dotStyle?.strokeWidth ?? 1.5)
        .attr("stroke-opacity", dotStyle?.strokeOpacity ?? 1)
        .style("fill-opacity", 0.8);
    });
  }, [
    areaStyle?.fill,
    areaStyle?.fillOpacity,
    areaStyle?.stroke,
    areaStyle?.strokeOpacity,
    areaStyle?.strokeWidth,
    curve,
    data,
    dotStyle?.fill,
    dotStyle?.fillOpacity,
    dotStyle?.stroke,
    dotStyle?.strokeOpacity,
    dotStyle?.strokeWidth,
    gridCircleCount,
    gridStyle?.axis?.stroke,
    gridStyle?.axis?.strokeOpacity,
    gridStyle?.axis?.strokeWidth,
    gridStyle?.grid?.fill,
    gridStyle?.grid?.fillOpacity,
    gridStyle?.grid?.stroke,
    gridStyle?.grid?.strokeOpacity,
    gridStyle?.grid?.strokeWidth,
    gridStyle?.text?.fill,
    gridStyle?.text?.fontSize,
    gridStyle?.text?.stroke,
    gridStyle?.text?.strokeWidth,
    height,
    marginBottom,
    marginLeft,
    marginRight,
    marginTop,
    width,
  ]);

  return <svg ref={chartRef}></svg>;
};
