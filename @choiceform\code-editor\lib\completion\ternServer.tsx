import { checkCursorInBinding } from "./utils";
import { Completion, CompletionContext, CompletionResult } from "@codemirror/autocomplete";
import { CompletionSource } from "./completion";
import { EditorView } from "@codemirror/view";

export enum AutocompleteDataType {
  OBJECT = "Object",
  NUMBER = "Number",
  ARRAY = "Array",
  FUNCTION = "Function",
  BOOLEAN = "Boolean",
  STRING = "String",
  UNKNOWN = "Unknown",
}

export function getDataType(type: string): AutocompleteDataType {
  if (type === "?") return AutocompleteDataType.UNKNOWN;
  else if (type === "number") return AutocompleteDataType.NUMBER;
  else if (type === "string") return AutocompleteDataType.STRING;
  else if (type === "bool") return AutocompleteDataType.BOOLEAN;
  else if (type === "array") return AutocompleteDataType.ARRAY;
  else if (/^fn\(/.test(type)) return AutocompleteDataType.FUNCTION;
  else if (/^\[/.test(type)) return AutocompleteDataType.ARRAY;
  else return AutocompleteDataType.OBJECT;
}

export class TernServer extends CompletionSource {
  completionSource(
    context: CompletionContext,
  ): CompletionResult | Promise<CompletionResult | null> | null {
    // console.log("complete pos:", context.pos, "\nselection:", context.state);
    const isFunction = this.completionController.codeType === "Function";
    const isCursorInBinding = checkCursorInBinding(context, isFunction);

    if (!isCursorInBinding) {
      return null;
    }

    const flag = context.matchBefore(/\{\{\s*/);

    if (
      context.matchBefore(/\w[\w.]*/) === null &&
      (isFunction || flag === null) &&
      context.matchBefore(/\${/) === null
    ) {
      return null;
    }

    const state = context.state;
    const pos = context.pos;

    const data = this.completionController.request(pos, state.sliceDoc());
    if (!data) return data;

    const options: Completion[] = [];

    for (const completion of data.completions) {
      const dataType = getDataType(completion.type);
      const completionOption: Completion = {
        type: dataType, // icon
        label: completion.name,
        detail: dataType, // short message after label
        // apply: (view: EditorView, c: Completion, from: number, to: number) => {
        //   view.dispatch({
        //     changes: {
        //       from: from,
        //       to: to,
        //       insert: `{{${completion.name}}}`,
        //     },
        //   });
        // },
        info:
          completion.doc === undefined
            ? undefined
            : () => {
                const dom = document.createElement("div");
                dom.innerHTML = `
                  <div class="flex-center" onclick='javascript:window.open("${completion.url}")' >
                    <svg width="16px" height="16px" class="hintSvg" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd" strokeLinecap="round">
                          <g transform="translate(8.000000, 8.000000) rotate(30.000000) translate(-8.000000, -8.000000) translate(5.000000, 1.500000)" stroke="#4965F2" strokeWidth="1.5">
                              <path d="M0,4.5 L0,3 C0,1.34314575 1.34314575,0 3,0 C4.65685425,0 6,1.34314575 6,3 L6,4.5 L6,4.5 M6,8.5 L6,10 C6,11.6568542 4.65685425,13 3,13 C1.34314575,13 0,11.6568542 0,10 L0,8.5 L0,8.5"></path>
                              <line x1="3" y1="4" x2="3" y2="9"></line>
                          </g>
                      </g>
                    </svg>
                    <span class="hintName">${completion.name}</span>
                  </div>
                  <span class="hintType">${completion.type}</span>
                  <span class="hintDoc">${completion.doc}</span>
                  `;
                return dom;
              },
        boost: -1,
      };
      options.push(completionOption);
    }

    const completions = {
      from: data.start,
      validFor: /^\w*$/,
      options,
    };
    // const token = context.state.sliceDoc(completions.from, context.pos);
    // const testFlag = completions.span.test(token)
    // log.log("Tern completeContext: ", context, "\ncompletionResult: ", completions, `\ntoken: ${token}, testFlag: ${testFlag}`);
    return completions;
  }
}
