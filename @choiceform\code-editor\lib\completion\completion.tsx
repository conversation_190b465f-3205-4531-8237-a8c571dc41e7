import { CompletionContext, CompletionResult } from "@codemirror/autocomplete";
import { CodeController } from "../code-controller";

export abstract class CompletionSource {
  completionController = new CodeController();
  constructor() {
    this.completionSource = this.completionSource.bind(this);
  }

  abstract completionSource(
    context: CompletionContext,
  ): CompletionResult | Promise<CompletionResult | null> | null;
}
