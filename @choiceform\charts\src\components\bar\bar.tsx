import { useEffect, useMemo, useRef, useState } from "react";
import * as d3 from "d3";
import { labels } from "./common/label";
import { useMaxLabelWidth } from "./hooks/use-label-width";
import { Axis } from "./common/axis";
import { Grid } from "./common/grid";
import { useRotatedDimensions } from "./hooks/use-rotated-dimensions";
import {
  axisAtom,
  barBackgroundAtom,
  barBackgroundStyleAtom,
  barCornerRadiusAtom,
  barLabelAtom,
  barPaddingAtom,
  barStyleAtom,
  calculateMeanAtom,
  calculateMedianAtom,
  gridAtom,
  gridStyleAtom,
  marginAtom,
  orientationAtom,
  scaleRangeAtom,
  tickArgumentsAtom,
  ticksAtom,
  tickSizeAtom,
  ticksFormatAtom,
  ticksLabelFormatAtom,
  ticksStyleAtom,
  barGradientAtom,
  specialColorMapAtom,
  barDataAtom,
  barGradientOrientationAtom,
  sizeAtom,
  calculateModeStyleAtom,
  calculateMedianStyle<PERSON>tom,
  dataSort<PERSON>tom,
  ticks<PERSON>ttach<PERSON><PERSON><PERSON>,
  animation<PERSON><PERSON><PERSON><PERSON>,
  axis<PERSON>abel<PERSON>tom,
} from "./atom";
import { use<PERSON>tom, useAtomValue } from "jotai";
import { v4 as uuidv4 } from "uuid";
import linearGradient from "./common/linear-gradient";
import { useCalculateMargins } from "./hooks/use-calculate-margin";
import { calculate } from "./common/calculate";
import { barBG } from "./common/background";

export interface BarChartProps {
  id: string;
}

export const BarChart = ({ id }: BarChartProps) => {
  const ref = useRef(null);
  const data = useAtomValue(barDataAtom(id));
  const dataSort = useAtomValue(dataSortAtom(id));
  const size = useAtomValue(sizeAtom(id));
  const orientation = useAtomValue(orientationAtom(id));
  const margin = useAtomValue(marginAtom(id));
  const axis = useAtomValue(axisAtom(id));
  const axisLabel = useAtomValue(axisLabelAtom(id));
  const tickArguments = useAtomValue(tickArgumentsAtom(id));
  const [ticksStyle, setTicksStyle] = useAtom(ticksStyleAtom(id));
  const scaleRange = useAtomValue(scaleRangeAtom(id));
  const ticks = useAtomValue(ticksAtom(id));
  const tickSize = useAtomValue(tickSizeAtom(id));
  const ticksFormat = useAtomValue(ticksFormatAtom(id));
  const ticksLabelFormat = useAtomValue(ticksLabelFormatAtom(id));
  const barPadding = useAtomValue(barPaddingAtom(id));
  const barCornerRadius = useAtomValue(barCornerRadiusAtom(id));
  const barGradient = useAtomValue(barGradientAtom(id));
  const barGradientOrientation = useAtomValue(barGradientOrientationAtom(id));
  const specialColorMap = useAtomValue(specialColorMapAtom(id));
  const barStyle = useAtomValue(barStyleAtom(id));
  const barBackground = useAtomValue(barBackgroundAtom(id));
  const barBackgroundStyle = useAtomValue(barBackgroundStyleAtom(id));
  const barLabel = useAtomValue(barLabelAtom(id));
  const grid = useAtomValue(gridAtom(id));
  const gridStyle = useAtomValue(gridStyleAtom(id));
  const calculateMean = useAtomValue(calculateMeanAtom(id));
  const calculateModeStyle = useAtomValue(calculateModeStyleAtom(id));
  const calculateMedian = useAtomValue(calculateMedianAtom(id));
  const calculateMedianStyle = useAtomValue(calculateMedianStyleAtom(id));
  const [animationState, setAnimationState] = useAtom(animationStateAtom(id));
  const ticksAttachment = useAtomValue(ticksAttachmentAtom(id));

  const [lengthLabel, setLengthLabel] = useState(false);

  const width = size.width;
  const height = size.height;

  // 提取标签
  const calculateHorizontalLabels =
    orientation === "horizontal" ? data.map((item) => item[0]) : data.map((item) => item[1]);
  const calculateVerticalLabels =
    orientation === "horizontal" ? data.map((item) => item[1]) : data.map((item) => item[0]);

  const { marginTop, marginRight, marginBottom, marginLeft, maxBottomLabelsWidth } =
    useCalculateMargins({
      ref,
      ticks,
      ticksStyle,
      tickSize,
      axis,
      axisLabel,
      margin,
      useMaxLabelWidth,
      useRotatedDimensions,
      ticksAttachment,
      calculateHorizontalLabels,
      calculateVerticalLabels,
    });

  useEffect(() => {
    if (orientation === "vertical") {
      if (lengthLabel) {
        setTicksStyle((prev) => ({
          ...prev,
          bottom: {
            ...prev.bottom,
            label: {
              ...prev.bottom?.label,
              rotate: 360 - 45,
              anchor: "end",
            },
          },
          top: {
            ...prev.top,
            label: {
              ...prev.top?.label,
              rotate: 45,
              anchor: "start",
            },
          },
        }));
      }
    }
    if (orientation === "horizontal") {
      setTicksStyle((prev) => ({
        ...prev,
        bottom: {
          ...prev.bottom,
          label: {
            ...prev.bottom?.label,
            rotate: 0,
            anchor: "middle",
          },
        },
        top: {
          ...prev.top,
          label: {
            ...prev.top?.label,
            rotate: 0,
            anchor: "middle",
          },
        },
      }));
    }
  }, [lengthLabel, orientation, setTicksStyle]);

  // 插值器渐变
  const gradient = useMemo(() => {
    return barStyle.gradient ?? ["#000", "#888", "#fff"];
  }, [barStyle.gradient]);
  const colorInterpolator = d3.interpolateRgbBasis(gradient);

  useEffect(() => {
    if (!ref.current) return;
    const svg = d3
      .select(ref.current)
      .attr("width", width + marginLeft + marginRight)
      .attr("height", height + marginTop + marginBottom);

    svg.selectAll("*").remove();

    // Calculate mean and median
    const mean = d3.mean(data, (d) => d[1]) ?? 0;
    const median = d3.median(data, (d) => d[1]) ?? 0;

    // 线性渐变
    const linearGradientId = uuidv4();
    linearGradient(svg, linearGradientId, gradient, barGradientOrientation);

    if (orientation === "vertical") {
      // Create scales
      const xScale = d3
        .scaleBand()
        .domain(data.map(([name]) => name))
        .range([0, width])
        .padding(barPadding.padding);

      const yScale = d3
        .scaleLinear()
        .domain(scaleRange ?? [0, d3.max(data, ([, value]) => value) || 0])
        .range([height, 0]);

      const gradientScale = d3
        .scaleLinear()
        .domain(scaleRange ?? [0, d3.max(data, ([, value]) => value) || 0])
        .range([0, 1]);

      Grid(
        svg,
        xScale,
        yScale,
        tickArguments,
        width,
        height,
        marginLeft,
        marginTop,
        animationState,
        grid,
        gridStyle,
      );

      if (barBackground) {
        barBG(
          svg,
          data,
          width,
          height,
          marginLeft,
          marginTop,
          xScale,
          yScale,
          orientation,
          animationState,
          barCornerRadius,
          barBackgroundStyle,
        );
      }

      if (dataSort.name === "ascending") {
        data.sort((a, b) => d3.ascending(a[0], b[0]));
      }
      if (dataSort.name === "descending") {
        data.sort((a, b) => d3.descending(a[0], b[0]));
      }
      if (dataSort.value === "ascending") {
        data.sort((a, b) => d3.ascending(a[1], b[1]));
      }
      if (dataSort.value === "descending") {
        data.sort((a, b) => d3.descending(a[1], b[1]));
      }
      xScale.domain(data.map((d) => d[0]));

      // Add bars
      const bars = svg
        .append("g")
        .attr("class", "bars")
        .attr("transform", `translate(${marginLeft},${marginTop})`);

      let counter = 0;
      bars
        .selectAll(".bar")
        .data(data)
        .enter()
        .append("rect")
        .attr("x", ([name]) => xScale(name) || 0)
        .attr("y", height)
        .attr("width", xScale.bandwidth())
        .attr("rx", barCornerRadius === "full" ? xScale.bandwidth() / 2 : barCornerRadius)
        .attr("ry", barCornerRadius === "full" ? xScale.bandwidth() / 2 : barCornerRadius)
        .attr("fill", function (d) {
          if (d[0] in specialColorMap) {
            return specialColorMap[d[0]];
          }
          if (barGradient === "linear") {
            return `url(#${linearGradientId})`;
          }
          if (barGradient === "interpolation") {
            return colorInterpolator(gradientScale(d[1]));
          }
          return barStyle?.fill ?? "black";
        })
        .attr("fill-opacity", barStyle?.fillOpacity ?? 1)
        .attr("stroke", barStyle?.stroke ?? "none")
        .attr("stroke-width", barStyle?.strokeWidth ?? 1)
        .each(function () {
          const transition = d3.select<SVGRectElement, [string, number]>(this);
          if (animationState.animation) {
            transition
              .transition()
              .delay((_, i) => i * animationState.delay)
              .duration(animationState.duration)
              .attr("height", (d) => height - yScale(d[1]))
              .attr("y", (d) => yScale(d[1]))
              .on("end", () => {
                counter++;
                if (counter === data.length) {
                  setAnimationState((prev) => ({
                    ...prev,
                    animation: false,
                  }));
                }
              });
          } else {
            transition.attr("height", (d) => height - yScale(d[1])).attr("y", (d) => yScale(d[1]));
          }
        });

      const xPosition = ([name]: string) => {
        const x = xScale(name);
        return x !== undefined ? x + xScale.bandwidth() / 2 + marginLeft : 0;
      };

      const yPosition = ([, value]: [string, number]) => {
        const y = yScale(value);
        const barHeight = height - y;
        const offset = barLabel?.offset ?? 10;
        switch (barLabel?.position) {
          case "start":
            return height - offset + marginTop;
          case "middle":
            return y + barHeight / 2 + marginTop;
          case "end":
            return y + offset + 12 + marginTop;
          case "outside":
            return y - offset + marginTop;
          default:
            return y + offset + 12 + marginTop;
        }
      };
      const textAnchor = barLabel?.style?.textAnchor ?? "middle";
      labels(svg, data, barLabel, textAnchor, xPosition, yPosition);

      // Add axes
      if (maxBottomLabelsWidth > xScale.bandwidth() + barPadding.padding) {
        setLengthLabel(true);
      }

      Axis(
        svg,
        xScale,
        yScale,
        "vertical",
        marginLeft,
        marginTop,
        marginRight,
        marginBottom,
        width,
        height,
        animationState,
        axis,
        axisLabel,
        ticks,
        tickSize,
        tickArguments,
        ticksStyle,
        ticksLabelFormat,
        ticksFormat,
        ticksAttachment,
        lengthLabel,
      );

      if (calculateMean) {
        calculate(
          svg,
          width,
          height,
          marginLeft,
          marginTop,
          xScale,
          yScale,
          mean,
          orientation,
          calculateModeStyle,
        );
      }

      if (calculateMedian) {
        calculate(
          svg,
          width,
          height,
          marginLeft,
          marginTop,
          xScale,
          yScale,
          median,
          orientation,
          calculateMedianStyle,
        );
      }
    } else {
      // Create scales
      const xScale = d3
        .scaleLinear()
        .domain(scaleRange ?? [0, d3.max(data, ([, value]) => value) || 0])
        .range([0, width]);

      const gradientScale = d3
        .scaleLinear()
        .domain(scaleRange ?? [0, d3.max(data, ([, value]) => value) || 0])
        .range([0, 1]);

      const yScale = d3
        .scaleBand()
        .domain(data.map(([name]) => name))
        .range([0, height])
        .padding(barPadding.padding);

      Grid(
        svg,
        xScale,
        yScale,
        tickArguments,
        width,
        height,
        marginLeft,
        marginTop,
        animationState,
        grid,
        gridStyle,
      );

      if (barBackground) {
        barBG(
          svg,
          data,
          width,
          height,
          marginLeft,
          marginTop,
          xScale,
          yScale,
          orientation,
          animationState,
          barCornerRadius,
          barBackgroundStyle,
        );
      }

      // Add bars
      const bars = svg
        .append("g")
        .attr("class", "bar")
        .attr("transform", `translate(${marginLeft},${marginTop})`);

      if (dataSort.name === "ascending") {
        data.sort((a, b) => d3.ascending(a[0], b[0]));
      }
      if (dataSort.name === "descending") {
        data.sort((a, b) => d3.descending(a[0], b[0]));
      }
      if (dataSort.value === "ascending") {
        data.sort((a, b) => d3.ascending(a[1], b[1]));
      }
      if (dataSort.value === "descending") {
        data.sort((a, b) => d3.descending(a[1], b[1]));
      }

      yScale.domain(data.map((d) => d[0]));
      let counter = 0;
      bars
        .selectAll(".bar")
        .data(data)
        .enter()
        .append("rect")
        .attr("x", 0)
        .attr("y", ([name]) => yScale(name) || 0)
        .attr("height", yScale.bandwidth())
        .attr("rx", barCornerRadius === "full" ? yScale.bandwidth() / 2 : barCornerRadius)
        .attr("ry", barCornerRadius === "full" ? yScale.bandwidth() / 2 : barCornerRadius)
        .attr("fill", function (d) {
          if (d[0] in specialColorMap) {
            return specialColorMap[d[0]];
          }
          if (barGradient === "linear") {
            return `url(#${linearGradientId})`;
          }
          if (barGradient === "interpolation") {
            return colorInterpolator(gradientScale(d[1]));
          }
          return barStyle?.fill ?? "black";
        })
        .attr("fill-opacity", barStyle?.fillOpacity ?? 1)
        .attr("stroke", barStyle?.stroke ?? "none")
        .attr("stroke-width", barStyle?.strokeWidth ?? 1)
        .each(function () {
          if (animationState.animation) {
            const rectTransition = d3
              .select<SVGRectElement, [string, number]>(this)
              .transition()
              .delay((_, i) => i * animationState.delay)
              .duration(animationState.duration);
            rectTransition
              .attr("width", (d) => xScale(d[1]))
              .on("end", () => {
                counter++;
                if (counter === data.length) {
                  setAnimationState((prev) => ({
                    ...prev,
                    animation: false,
                  }));
                }
              });
          } else {
            d3.select<SVGRectElement, [string, number]>(this).attr("width", (d) => xScale(d[1]));
          }
        });

      const yPosition = ([name]: string) => {
        const y = yScale(name);
        return y !== undefined ? y + yScale.bandwidth() / 2 + marginTop : 0;
      };

      const xPosition = ([, value]: [string, number]) => {
        const x = xScale(value);
        const offset = barLabel?.offset ?? 10;
        switch (barLabel?.position) {
          case "start":
            return marginLeft + offset;
          case "middle":
            return marginLeft + x / 2;
          case "end":
            return marginLeft + x - offset;
          case "outside":
            return marginLeft + x + offset;
          default:
            return marginLeft + x + offset;
        }
      };

      const textAnchorPosition = () => {
        switch (barLabel?.position) {
          case "start":
            return "start";
          case "middle":
            return "middle";
          case "end":
            return "end";
          case "outside":
            return "start";
          default:
            return "start";
        }
      };

      const textAnchor = barLabel?.style?.textAnchor ?? textAnchorPosition();
      labels(svg, data, barLabel, textAnchor, xPosition, yPosition);

      // Add axes
      Axis(
        svg,
        xScale,
        yScale,
        "horizontal",
        marginLeft,
        marginTop,
        marginRight,
        marginBottom,
        width,
        height,
        animationState,
        axis,
        axisLabel,
        ticks,
        tickSize,
        tickArguments,
        ticksStyle,
        ticksLabelFormat,
        ticksFormat,
        ticksAttachment,
      );

      if (calculateMean) {
        calculate(
          svg,
          width,
          height,
          marginLeft,
          marginTop,
          xScale,
          yScale,
          mean,
          orientation,
          calculateModeStyle,
        );
      }

      if (calculateMedian) {
        calculate(
          svg,
          width,
          height,
          marginLeft,
          marginTop,
          xScale,
          yScale,
          median,
          orientation,
          calculateMedianStyle,
        );
      }
    }
  }, [
    ticksAttachment,
    axis,
    barBackground,
    barBackgroundStyle,
    barCornerRadius,
    barGradient,
    barGradientOrientation,
    barLabel,
    barPadding,
    barStyle?.fill,
    barStyle?.fillOpacity,
    barStyle?.stroke,
    barStyle?.strokeWidth,
    calculateMean,
    calculateMedian,
    calculateMedianStyle,
    calculateModeStyle,
    colorInterpolator,
    data,
    dataSort.name,
    dataSort.value,
    gradient,
    grid,
    gridStyle,
    height,
    lengthLabel,
    marginBottom,
    marginLeft,
    marginRight,
    marginTop,
    maxBottomLabelsWidth,
    orientation,
    scaleRange,
    specialColorMap,
    tickArguments,
    tickSize,
    ticks,
    ticksFormat,
    ticksLabelFormat,
    ticksStyle,
    width,
    animationState,
    setAnimationState,
    axisLabel,
  ]);

  return (
    <svg
      style={{
        overflow: "visible",
      }}
      ref={ref}
    />
  );
};
