import { useEffect } from "react";
import {
  Accordion,
  AccordionPanel,
  Button,
  Checkbox,
  Input,
  NativeSelect,
  NumberField,
  Radio,
  RadioGroup,
  ScrollArea,
  Slider,
  SvgIcon,
  SvgIconName,
  ColorPickerSimple,
  CheckboxGroup,
  Textarea,
  Scp,
  ScpItem,
  Divider,
} from "@choiceform/ui-react";
import { useAtom, useAtomValue } from "jotai";
import { Color, parseColor } from "@react-stately/color";
import {
  animationStateAtom,
  axisAtom,
  axisLabelAtom,
  barBackgroundAtom,
  barBackgroundStyleAtom,
  barCornerRadiusAtom,
  barDataAtom,
  barGradientAtom,
  barGradientOrientationAtom,
  barLabelAtom,
  barPaddingAtom,
  barStyleAtom,
  calculateAtom,
  calculateMeanAtom,
  calculateMedianAtom,
  calculateMedianStyleAtom,
  calculateModeStyleAtom,
  dataSortAtom,
  gridAtom,
  gridStyleAtom,
  marginAtom,
  multiBarDataAtom,
  orientationAtom,
  scaleRangeAtom,
  sizeAtom,
  specialColorMapAtom,
  stackedModeAtom,
  thresholdsAtom,
  thresholdsNumAtom,
  tickArgumentsAtom,
  tickSizeAtom,
  ticksAtom,
  ticksAttachmentAtom,
  ticksFormatAtom,
  ticksLabelFormatAtom,
  ticksStyleAtom,
} from "./atom";
import {
  barLabelProps,
  dataSortProps,
  directionsBooleanProps,
  directionsNumberProps,
  gridStyleProps,
  ticksAttachmentProps,
  ticksStateProps,
  ticksStyleProps,
} from "./type";

export interface ControlProps {
  id: string;
}

export const Control = ({ id }: ControlProps) => {
  const data = useAtomValue(barDataAtom(id));
  const multiBarData = useAtomValue(multiBarDataAtom(id));
  const [dataSort, setDataSort] = useAtom(dataSortAtom(id));
  const [size, setSize] = useAtom(sizeAtom(id));
  const [orientation, setOrientation] = useAtom(orientationAtom(id));
  const [margin, setMargin] = useAtom(marginAtom(id));
  const [axis, setAxis] = useAtom(axisAtom(id));
  const [axisLabel, setAxisLabel] = useAtom(axisLabelAtom(id));
  const [tickArguments, setTickArguments] = useAtom(tickArgumentsAtom(id));
  const [ticksStyle, setTicksStyle] = useAtom(ticksStyleAtom(id));
  const [scaleRange, setScaleRange] = useAtom(scaleRangeAtom(id));
  const [ticks, setTicks] = useAtom(ticksAtom(id));
  const [tickSize, setTickSize] = useAtom(tickSizeAtom(id));
  const [ticksFormat, setTicksFormat] = useAtom(ticksFormatAtom(id));
  const [ticksLabelFormat, setTicksLabelFormat] = useAtom(ticksLabelFormatAtom(id));
  const [barPadding, setBarPadding] = useAtom(barPaddingAtom(id));
  const [barCornerRadius, setBarCornerRadius] = useAtom(barCornerRadiusAtom(id));
  const [barGradient, setBarGradient] = useAtom(barGradientAtom(id));
  const [, setBarGradientOrientation] = useAtom(barGradientOrientationAtom(id));
  const [specialColorMap, setSpecialColorMap] = useAtom(specialColorMapAtom(id));
  const [barStyle, setBarStyle] = useAtom(barStyleAtom(id));
  const [barLabel, setBarLabel] = useAtom(barLabelAtom(id));
  const [barBackground, setBarBackground] = useAtom(barBackgroundAtom(id));
  const [barBackgroundStyle, setBarBackgroundStyle] = useAtom(barBackgroundStyleAtom(id));
  const [grid, setGrid] = useAtom(gridAtom(id));
  const [gridStyle, setGridStyle] = useAtom(gridStyleAtom(id));
  const [calculateMean, setCalculateMean] = useAtom(calculateMeanAtom(id));
  const [calculateModeStyle, setCalculateModeStyle] = useAtom(calculateModeStyleAtom(id));
  const [calculateMedian, setCalculateMedian] = useAtom(calculateMedianAtom(id));
  const [calculateMedianStyle, setCalculateMedianStyle] = useAtom(calculateMedianStyleAtom(id));
  const [calculate, setCalculate] = useAtom(calculateAtom(id));
  const [animationState, setAnimationState] = useAtom(animationStateAtom(id));
  const [ticksAttachment, setTicksAttachment] = useAtom(ticksAttachmentAtom(id));
  const [thresholdsNum, setThresholdsNum] = useAtom(thresholdsNumAtom(id));
  const [thresholds, setThresholds] = useAtom(thresholdsAtom(id));
  const [stackedMode, setStackedMode] = useAtom(stackedModeAtom(id));

  useEffect(() => {
    // Initialize barStyle.fillArray with the same length as multiBarData[0].values
    if (
      multiBarData.length > 0 &&
      multiBarData[0].values &&
      Array.isArray(multiBarData[0].values)
    ) {
      const fillArrayLength = multiBarData[0].values.length;

      if (Array.isArray(barStyle.fillArray) && barStyle.fillArray.length < fillArrayLength) {
        setBarStyle((prevBarStyle) => {
          const newFillArray = Array(fillArrayLength).fill(""); // Fill with default color
          prevBarStyle.fillArray?.forEach((color, i) => (newFillArray[i] = color)); // Copy existing colors

          return {
            ...prevBarStyle,
            fillArray: newFillArray,
          };
        });
      }
    }
  }, [multiBarData, barStyle.fillArray]);

  return (
    <ScrollArea
      classNames={{
        content: "pt-8 pb-16",
      }}
    >
      <Accordion>
        <AccordionPanel header="Dimensions and Positioning">
          <div className="grid grid-cols-2 gap-4">
            <Slider
              setSize="xs"
              label="Width"
              minValue={0}
              maxValue={1000}
              value={size.width}
              onChange={(value) => typeof value === "number" && setSize({ ...size, width: value })}
            />
            <Slider
              setSize="xs"
              label="Height"
              minValue={0}
              maxValue={1000}
              value={size.height}
              onChange={(value) =>
                typeof value === "number" && setSize({ ...size, height: value })
              }
            />
          </div>

          <Divider />

          <RadioGroup
            label="Orientation"
            orientation="horizontal"
            setSize="sm"
            value={orientation}
            onValueChange={(value) =>
              typeof value === "string" && setOrientation(value as "vertical" | "horizontal")
            }
          >
            <Radio
              key={"vertical"}
              value={"vertical"}
              label="Vertical"
            />
            <Radio
              key={"horizontal"}
              value={"horizontal"}
              label="Horizontal"
            />
          </RadioGroup>

          <Divider />

          <Checkbox
            setSize="sm"
            label="Animation Enable"
            isSelected={animationState.animation}
            onValueChange={() =>
              setAnimationState({ ...animationState, animation: !animationState.animation })
            }
          />

          <div className="grid grid-cols-2 gap-4">
            <Slider
              setSize="xs"
              label="Duration"
              minValue={0}
              maxValue={1000}
              value={animationState.duration}
              onChange={(value) =>
                typeof value === "number" &&
                setAnimationState({ ...animationState, duration: value })
              }
            />
            <Slider
              setSize="xs"
              label="Delay"
              minValue={0}
              maxValue={1000}
              value={animationState.delay}
              onChange={(value) =>
                typeof value === "number" && setAnimationState({ ...animationState, delay: value })
              }
            />
          </div>

          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() =>
              setAnimationState({
                animation: false,
                duration: 1000,
                delay: 200,
              })
            }
          />

          <Divider />

          <div className="grid grid-cols-2 gap-4">
            {["top", "right", "bottom", "left"].map((p) => (
              <Slider
                key={p}
                setSize="xs"
                label={`Margin ${p}`}
                minValue={0}
                maxValue={100}
                value={margin[p as keyof typeof margin]}
                onChange={(value) =>
                  typeof value === "number" && setMargin({ ...margin, [p]: value })
                }
              />
            ))}
          </div>
          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() => setMargin({ top: 0, right: 0, bottom: 0, left: 0 })}
          />

          <Divider />

          <div className="grid grid-cols-2 gap-4">
            <NativeSelect
              classNames={{
                label: "text-xs",
              }}
              label="Data sort Name"
              setSize="sm"
              options={[
                {
                  value: "none",
                  label: "None",
                },
                {
                  value: "ascending",
                  label: "Ascending",
                },
                {
                  value: "descending",
                  label: "Descending",
                },
              ]}
              value={dataSort.name}
              onValueChange={(e) =>
                setDataSort({
                  ...dataSort,
                  name: e.target.value as dataSortProps,
                  value: "none",
                })
              }
            />
            <NativeSelect
              classNames={{
                label: "text-xs",
              }}
              label="Data sort Value"
              setSize="sm"
              options={[
                {
                  value: "none",
                  label: "None",
                },
                {
                  value: "ascending",
                  label: "Ascending",
                },
                {
                  value: "descending",
                  label: "Descending",
                },
              ]}
              value={dataSort.value}
              onValueChange={(e) =>
                setDataSort({
                  ...dataSort,
                  value: e.target.value as dataSortProps,
                  name: "none",
                })
              }
            />
          </div>
        </AccordionPanel>

        <AccordionPanel header="Histogram Settings">
          <NativeSelect
            label="Thresholds"
            classNames={{
              label: "text-xs",
            }}
            setSize="sm"
            value={thresholds}
            onValueChange={(e) => {
              setThresholds(e.target.value as "sturges" | "scott" | "freedmanDiaconis" | "binNum");
            }}
            options={[
              { value: "sturges", label: "Sturges" },
              { value: "scott", label: "Scott" },
              { value: "freedmanDiaconis", label: "Freedman Diaconis" },
              { value: "binNum", label: "Bin Num" },
            ]}
          />

          {thresholds === "binNum" && (
            <Slider
              setSize="xs"
              label="Bin number"
              minValue={1}
              maxValue={1000}
              value={thresholdsNum}
              onChange={(value) => typeof value === "number" && setThresholdsNum(value)}
            />
          )}
        </AccordionPanel>

        <AccordionPanel header="Stacked Settings">
          <NativeSelect
            label="Stacked mode"
            classNames={{
              label: "text-xs",
            }}
            setSize="sm"
            value={stackedMode}
            onValueChange={(e) => {
              setStackedMode(e.target.value as "value" | "percentage");
            }}
            options={[
              { value: "value", label: "Value" },
              { value: "percentage", label: "Percentage" },
            ]}
          />
        </AccordionPanel>

        <AccordionPanel header="Bar Chart Settings">
          <Slider
            setSize="xs"
            label="Bar padding"
            minValue={0}
            maxValue={1}
            step={0.1}
            value={barPadding.padding}
            onChange={(value) =>
              typeof value === "number" && setBarPadding({ ...barPadding, padding: value })
            }
          />

          <Slider
            setSize="xs"
            label="Bar outer padding"
            minValue={0}
            maxValue={1}
            step={0.1}
            value={barPadding.paddingOuter}
            onChange={(value) =>
              typeof value === "number" && setBarPadding({ ...barPadding, paddingOuter: value })
            }
          />

          <Slider
            setSize="xs"
            label="Bar inner padding"
            minValue={0}
            maxValue={1}
            step={0.1}
            value={barPadding.paddingInner}
            onChange={(value) =>
              typeof value === "number" && setBarPadding({ ...barPadding, paddingInner: value })
            }
          />

          <Divider />

          <Slider
            setSize="xs"
            label="Bar corner radius"
            minValue={0}
            maxValue={10}
            value={typeof barCornerRadius === "number" ? barCornerRadius : 0}
            onChange={(value) => typeof value === "number" && setBarCornerRadius(value)}
          />

          <Checkbox
            setSize="sm"
            label="Corner radius full"
            isSelected={barCornerRadius === "full"}
            onValueChange={() => setBarCornerRadius(barCornerRadius === "full" ? 0 : "full")}
          />

          <Divider />

          <div className="flex flex-wrap gap-4">
            {multiBarData.length > 0 ? (
              multiBarData[0].values &&
              Array.isArray(multiBarData[0].values) &&
              multiBarData[0].values.map((_, i) => (
                <ColorPickerSimple
                  key={`value-${i}`}
                  color={parseColor(
                    Array.isArray(barStyle.fillArray) && barStyle.fillArray[i]
                      ? barStyle.fillArray[i]
                      : "#000000",
                  ).toFormat("hsl")}
                  onColorChange={(color) => {
                    setBarStyle((prevBarStyle) => {
                      let newFillArray;

                      if (Array.isArray(prevBarStyle.fillArray)) {
                        newFillArray = prevBarStyle.fillArray.slice(); // Copy existing array
                      } else {
                        newFillArray = Array(multiBarData[0].values.length).fill(undefined); // Fill with undefined
                      }

                      newFillArray[i] = color.toString("css"); // Set the new color

                      return {
                        ...prevBarStyle,
                        fillArray: newFillArray,
                      };
                    });
                  }}
                  colorSwatchProps={{
                    className: "w-6 h-6 rounded-full",
                  }}
                  buttonProps={{
                    text: `value-${i}`,
                    setSize: "sm",
                    classNames: {
                      base: "pr-1 pl-2",
                    },
                  }}
                />
              ))
            ) : (
              <ColorPickerSimple
                color={parseColor(barStyle.fill ?? "#000000").toFormat("hsl")}
                onColorChange={(color) =>
                  setBarStyle({ ...barStyle, fill: color.toString("css") })
                }
                colorSwatchProps={{
                  className: "w-6 h-6 rounded-full",
                }}
                buttonProps={{
                  text: "Bar fill",
                  setSize: "sm",
                  classNames: {
                    base: "pr-1 pl-2",
                  },
                }}
              />
            )}
          </div>
          <Slider
            setSize="xs"
            label="Fill opacity"
            minValue={0}
            maxValue={1}
            step={0.1}
            value={barStyle.fillOpacity}
            onChange={(value) =>
              typeof value === "number" && setBarStyle({ ...barStyle, fillOpacity: value })
            }
          />

          <Checkbox
            setSize="sm"
            label="Show bar stroke"
            isSelected={barStyle.stroke !== "none"}
            onValueChange={() =>
              setBarStyle({ ...barStyle, stroke: barStyle.stroke === "none" ? "#000000" : "none" })
            }
          />

          {barStyle.stroke !== "none" && (
            <>
              <ColorPickerSimple
                color={parseColor(
                  barStyle.stroke
                    ? barStyle.stroke === "none"
                      ? "#000000"
                      : barStyle.stroke
                    : "#000000",
                ).toFormat("hsl")}
                onColorChange={(color) =>
                  setBarStyle({ ...barStyle, stroke: color.toString("css") })
                }
                colorSwatchProps={{
                  className: "w-6 h-6 rounded-full",
                }}
                buttonProps={{
                  text: "Bar stroke",
                  setSize: "sm",
                  classNames: {
                    base: "pr-1 pl-2 self-start",
                  },
                }}
              />
              <Slider
                setSize="xs"
                label="Stroke width"
                minValue={0}
                maxValue={10}
                value={barStyle.strokeWidth}
                onChange={(value) =>
                  typeof value === "number" && setBarStyle({ ...barStyle, strokeWidth: value })
                }
              />
            </>
          )}

          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() =>
              setBarStyle({
                fill: "#000000",
                fillArray: [],
                fillOpacity: 1,
                stroke: "none",
                strokeWidth: 1,
              })
            }
          />

          <Divider />

          <div className="grid grid-cols-2 gap-4">
            <NativeSelect
              classNames={{
                label: "text-xs",
              }}
              label="Gradient type"
              setSize="sm"
              value={barGradient}
              onValueChange={(e) => {
                setBarGradient(e.target.value as "interpolation" | "linear");
              }}
              options={[
                { value: "none", label: "None" },
                { value: "interpolation", label: "Interpolation" },
                { value: "linear", label: "Linear" },
              ]}
            />

            <NativeSelect
              classNames={{
                label: "text-xs",
              }}
              label="Gradient orientation"
              setSize="sm"
              isDisabled={barGradient !== "linear"}
              value={barGradient}
              onValueChange={(e) => {
                setBarGradientOrientation(e.target.value as "vertical" | "horizontal");
              }}
              options={[
                { value: "vertical", label: "Vertical" },
                { value: "horizontal", label: "Horizontal" },
              ]}
            />
          </div>

          {barGradient && (
            <>
              {multiBarData.length > 0 ? (
                <>
                  <div className="flex flex-wrap gap-4">
                    {multiBarData[0].values &&
                      Array.isArray(multiBarData[0].values) &&
                      multiBarData[0].values.map((_, i) => (
                        <ColorPickerSimple
                          key={`value-${i}`}
                          color={parseColor(
                            Array.isArray(barStyle.gradientArray)
                              ? barStyle.gradientArray[i]
                                ? barStyle.gradientArray[i][0]
                                : "#000000"
                              : barStyle.gradientArray
                                ? barStyle.gradientArray[0]
                                : "#000000",
                          ).toFormat("hsl")}
                          onColorChange={(color) => {
                            let newGradientArray;
                            if (
                              Array.isArray(barStyle.gradientArray) &&
                              Array.isArray(barStyle.gradientArray[0])
                            ) {
                              // 如果 barStyle.gradientArray 已经是二维数组，我们就直接更新对应的颜色
                              newGradientArray = barStyle.gradientArray.slice(); // 创建一个新的数组，以避免直接修改原数组
                              newGradientArray[i] = [
                                color.toString("css"),
                                newGradientArray[i][1],
                              ];
                            } else {
                              // 如果 barStyle.gradientArray 不是二维数组，我们就创建一个新的二维数组，并将用户选择的颜色添加到对应的位置
                              newGradientArray = new Array(multiBarData[0].values.length).fill([
                                "#000000",
                                "#000000",
                              ]);
                              newGradientArray[i] = [color.toString("css"), "#000000"];

                              const newBarStyle = {
                                ...barStyle,
                                gradientArray: newGradientArray,
                              };
                              setBarStyle(newBarStyle);
                            }
                          }}
                          colorSwatchProps={{
                            className: "w-6 h-6 rounded-full",
                          }}
                          buttonProps={{
                            text: `start-${i}`,
                            setSize: "sm",
                            classNames: {
                              base: "pr-1 pl-2",
                            },
                          }}
                        />
                      ))}
                  </div>

                  <div className="flex flex-wrap gap-4">
                    {multiBarData[0].values &&
                      Array.isArray(multiBarData[0].values) &&
                      multiBarData[0].values.map((_, i) => (
                        <>
                          <ColorPickerSimple
                            key={`value-${i}`}
                            color={parseColor(
                              Array.isArray(barStyle.gradientArray)
                                ? barStyle.gradientArray[i]
                                  ? barStyle.gradientArray[i][1]
                                  : "#000000"
                                : barStyle.gradientArray
                                  ? barStyle.gradientArray[1]
                                  : "#000000",
                            ).toFormat("hsl")}
                            onColorChange={(color) => {
                              let newGradientArray;
                              if (
                                Array.isArray(barStyle.gradientArray) &&
                                Array.isArray(barStyle.gradientArray[0])
                              ) {
                                // 如果 barStyle.gradientArray 已经是二维数组，我们就直接更新对应的颜色
                                newGradientArray = barStyle.gradientArray.slice(); // 创建一个新的数组，以避免直接修改原数组
                                newGradientArray[i] = [
                                  newGradientArray[i][0],
                                  color.toString("css"),
                                ];
                              } else {
                                // 如果 barStyle.gradientArray 不是二维数组，我们就创建一个新的二维数组，并将用户选择的颜色添加到对应的位置
                                newGradientArray = new Array(multiBarData[0].values.length).fill([
                                  "#000000",
                                  "#000000",
                                ]);
                                newGradientArray[i] = ["#000000", color.toString("css")];
                              }

                              const newBarStyle = {
                                ...barStyle,
                                gradientArray: newGradientArray,
                              };

                              setBarStyle(newBarStyle);
                            }}
                            colorSwatchProps={{
                              className: "w-6 h-6 rounded-full",
                            }}
                            buttonProps={{
                              text: `end-${i}`,
                              setSize: "sm",
                              classNames: {
                                base: "pr-1 pl-2",
                              },
                            }}
                          />
                        </>
                      ))}
                  </div>
                </>
              ) : (
                <div className="flex flex-wrap gap-4">
                  <ColorPickerSimple
                    color={parseColor(
                      barStyle && barStyle.gradient ? barStyle.gradient[0] : "#000000",
                    ).toFormat("hsl")}
                    onColorChange={(color) =>
                      setBarStyle((oldBarStyle) => ({
                        ...oldBarStyle,
                        gradient:
                          oldBarStyle && oldBarStyle.gradient
                            ? [color.toString("css"), oldBarStyle.gradient[1]]
                            : [color.toString("css"), "#000000"],
                      }))
                    }
                    colorSwatchProps={{
                      className: "w-6 h-6 rounded-full",
                    }}
                    buttonProps={{
                      text: "Gradient start",
                      setSize: "sm",
                      classNames: {
                        base: "pr-1 pl-2",
                      },
                    }}
                  />

                  <ColorPickerSimple
                    color={parseColor(
                      barStyle && barStyle.gradient ? barStyle.gradient[1] : "#000000",
                    ).toFormat("hsl")}
                    onColorChange={(color) =>
                      setBarStyle((oldBarStyle) => ({
                        ...oldBarStyle,
                        gradient:
                          oldBarStyle && oldBarStyle.gradient
                            ? [oldBarStyle.gradient[0], color.toString("css")]
                            : ["#000000", color.toString("css")],
                      }))
                    }
                    colorSwatchProps={{
                      className: "w-6 h-6 rounded-full",
                    }}
                    buttonProps={{
                      text: "Gradient end",
                      setSize: "sm",
                      classNames: {
                        base: "pr-1 pl-2",
                      },
                    }}
                  />
                </div>
              )}
            </>
          )}

          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() => setBarGradient(undefined)}
          />

          <Divider />

          <span className="text-sm">Special color map</span>
          {data.map((d) => (
            <ColorPickerSimple
              key={d[0]}
              color={parseColor(specialColorMap[d[0]] ?? "#000000").toFormat("hsl")}
              onColorChange={(color) => {
                const newSpecialColorMap = {
                  ...specialColorMap,
                  [d[0]]: color.toString("css"),
                };
                setSpecialColorMap(newSpecialColorMap);
              }}
              colorSwatchProps={{
                className: "w-6 h-6 rounded-full",
              }}
              buttonProps={{
                text: d[0],
                setSize: "sm",
                classNames: {
                  base: "pr-1 pl-2",
                },
              }}
            />
          ))}

          {multiBarData.map((fruit, fruitIndex) => (
            <div
              key={fruitIndex}
              className="flex flex-wrap items-center gap-4"
            >
              {fruit.values.map((value, valueIndex) => (
                <ColorPickerSimple
                  key={valueIndex}
                  color={parseColor(
                    specialColorMap[`${fruit.name}-${valueIndex + 1}`] ??
                      specialColorMap[0] ??
                      "#000000",
                  ).toFormat("hsl")}
                  onColorChange={(color) => {
                    const newSpecialColorMap = {
                      ...specialColorMap,
                      [`${fruit.name}-${valueIndex + 1}`]: color.toString("css"),
                    };
                    setSpecialColorMap(newSpecialColorMap);
                  }}
                  colorSwatchProps={{
                    className: "w-6 h-6 rounded-full",
                  }}
                  buttonProps={{
                    text: `${fruit.name}-${valueIndex + 1}`,
                    setSize: "sm",
                    classNames: {
                      base: "pr-1 pl-2",
                    },
                  }}
                />
              ))}
            </div>
          ))}

          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() => setSpecialColorMap({})}
          />

          <Divider />

          <Checkbox
            setSize="sm"
            label="Show bar background"
            isSelected={barBackground}
            onValueChange={() => setBarBackground(!barBackground)}
          />

          {barBackground && (
            <>
              <ColorPickerSimple
                color={parseColor(barBackgroundStyle.fill ?? "#000000").toFormat("hsl")}
                onColorChange={(color) =>
                  setBarBackgroundStyle({ ...barBackgroundStyle, fill: color.toString("css") })
                }
                colorSwatchProps={{
                  className: "w-6 h-6 rounded-full",
                }}
                buttonProps={{
                  text: "Fill",
                  setSize: "sm",
                  classNames: {
                    base: "pr-1 pl-2 self-start",
                  },
                }}
              />
              <Slider
                setSize="xs"
                label="Fill opacity"
                minValue={0}
                maxValue={1}
                step={0.1}
                value={barBackgroundStyle.fillOpacity}
                onChange={(value) =>
                  typeof value === "number" &&
                  setBarBackgroundStyle({ ...barBackgroundStyle, fillOpacity: value })
                }
              />
            </>
          )}

          <Divider />

          <RadioGroup
            orientation="horizontal"
            label="Bar label type"
            setSize="sm"
            value={barLabel.type === undefined ? "none" : barLabel.type}
            onValueChange={(value) => {
              setBarLabel({
                ...barLabel,
                type:
                  value === "none" ? undefined : (value as undefined | "name" | "value" | "both"),
              });
            }}
          >
            <Radio
              value="none"
              label="None"
            />
            <Radio
              value="name"
              label="Name"
            />
            <Radio
              value="value"
              label="Value"
            />
            <Radio
              value="both"
              label="Both"
            />
          </RadioGroup>

          {barLabel.type && (
            <>
              <RadioGroup
                orientation="horizontal"
                label="Bar label position"
                setSize="sm"
                value={barLabel.position}
                onValueChange={(value) => {
                  setBarLabel({
                    ...barLabel,
                    position: value as "start" | "middle" | "end" | "outside",
                  });
                }}
              >
                <Radio
                  value="start"
                  label="Start"
                />
                <Radio
                  value="middle"
                  label="Middle"
                />
                <Radio
                  value="end"
                  label="End"
                />
                <Radio
                  value="outside"
                  label="Outside"
                />
              </RadioGroup>

              <span>Bar label style</span>
              <div className="flex gap-4">
                <ColorPickerSimple
                  color={parseColor(barLabel?.style?.fill ?? "#000000").toFormat("hsl")}
                  onColorChange={(color) =>
                    setBarLabel({
                      ...barLabel,
                      style: { ...barLabel.style, fill: color.toString("css") },
                    })
                  }
                  colorSwatchProps={{
                    className: "w-6 h-6 rounded-full",
                  }}
                  buttonProps={{
                    text: "Fill",
                    setSize: "sm",
                    classNames: {
                      base: "pr-1 pl-2",
                    },
                  }}
                />
                <ColorPickerSimple
                  color={parseColor(barLabel?.style?.stroke ?? "#000000").toFormat("hsl")}
                  onColorChange={(color) =>
                    setBarLabel({
                      ...barLabel,
                      style: { ...barLabel.style, stroke: color.toString("css") },
                    })
                  }
                  colorSwatchProps={{
                    className: "w-6 h-6 rounded-full",
                  }}
                  buttonProps={{
                    text: "Stroke",
                    setSize: "sm",
                    classNames: {
                      base: "pr-1 pl-2",
                    },
                  }}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <Slider
                  setSize="xs"
                  label="Font size"
                  minValue={8}
                  maxValue={24}
                  value={barLabel?.style?.fontSize ?? 12}
                  onChange={(value) =>
                    typeof value === "number" &&
                    setBarLabel({
                      ...barLabel,
                      style: { ...barLabel.style, fontSize: value },
                    })
                  }
                />
                <Slider
                  setSize="xs"
                  label="Stroke width"
                  minValue={0}
                  maxValue={10}
                  value={barLabel?.style?.strokeWidth ?? 0}
                  onChange={(value) =>
                    typeof value === "number" &&
                    setBarLabel({
                      ...barLabel,
                      style: { ...barLabel.style, strokeWidth: value },
                    })
                  }
                />
              </div>

              <NativeSelect
                label="Text anchor"
                setSize="sm"
                value={barLabel?.style?.textAnchor ?? "start"}
                onValueChange={(e) =>
                  setBarLabel({
                    ...barLabel,
                    style: {
                      ...barLabel.style,
                      textAnchor: e.target.value as "start" | "middle" | "end",
                    },
                  })
                }
                options={[
                  { value: "start", label: "Start" },
                  { value: "middle", label: "Middle" },
                  { value: "end", label: "End" },
                ]}
              />
            </>
          )}

          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() =>
              setBarLabel({
                ...barLabel,
                style: {},
              })
            }
          />
        </AccordionPanel>

        <AccordionPanel header="Axis Settings">
          <div className="grid grid-cols-2 gap-4">
            <Slider
              setSize="xs"
              label="Scale range min"
              minValue={0}
              maxValue={100}
              value={scaleRange ? scaleRange[0] : 0}
              onChange={(value) =>
                typeof value === "number" &&
                setScaleRange((currentRange) => [value, currentRange ? currentRange[1] : 0])
              }
            />

            <Slider
              setSize="xs"
              label="Scale range max"
              minValue={1}
              maxValue={1000}
              value={scaleRange ? scaleRange[1] : 0}
              onChange={(value) =>
                typeof value === "number" &&
                setScaleRange((currentRange) => [currentRange ? currentRange[0] : 0, value])
              }
            />
          </div>
          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() => setScaleRange(undefined)}
          />

          <CheckboxGroup
            label="Axis"
            setSize="sm"
            orientation="horizontal"
            value={Object.keys(axis).filter((key) => axis[key as keyof typeof axis])}
            onValueChange={(value) => {
              setAxis(
                value.reduce((acc, key) => {
                  acc[key as keyof typeof axis] = true;
                  return acc;
                }, {} as directionsBooleanProps),
              );
            }}
          >
            {["top", "bottom", "left", "right"].map((p) => (
              <Checkbox
                key={p}
                value={p}
                label={p}
              />
            ))}
          </CheckboxGroup>

          <div className="grid grid-cols-2 gap-4">
            {["top", "bottom", "left", "right"].map((p) => (
              <Input
                key={p}
                label={`Axis label ${p}`}
                classNames={{
                  label: "text-xs",
                }}
                setSize="sm"
                value={axisLabel[p as keyof typeof axisLabel]?.label}
                onValueChange={(value) => {
                  const newAxisLabel: any = {
                    ...axisLabel,
                    [p]: {
                      ...axisLabel[p as keyof typeof axisLabel],
                      label: value,
                    },
                  };
                  setAxisLabel(newAxisLabel);
                }}
              />
            ))}
          </div>

          <Divider />

          <div className="grid grid-cols-2 gap-4">
            {["top", "bottom", "left", "right"].map((p) => (
              <Slider
                key={p}
                setSize="xs"
                label={`${p} font size`}
                minValue={8}
                maxValue={24}
                value={axisLabel[p as keyof typeof axisLabel]?.style?.fontSize}
                onChange={(value) => {
                  const newAxisLabel: any = {
                    ...axisLabel,
                    [p]: {
                      ...axisLabel[p as keyof typeof axisLabel],
                      style: {
                        ...axisLabel[p as keyof typeof axisLabel]?.style,
                        fontSize: value,
                      },
                    },
                  };
                  setAxisLabel(newAxisLabel);
                }}
              />
            ))}
          </div>

          <Divider />

          <div className="grid grid-cols-2 gap-4">
            {["top", "bottom", "left", "right"].map((p) => (
              <Slider
                fillOffset={0}
                key={p}
                setSize="xs"
                label={`${p} translateY`}
                minValue={-100}
                maxValue={100}
                defaultValue={0}
                value={axisLabel[p as keyof typeof axisLabel]?.style?.translateY}
                onChange={(value) => {
                  const newAxisLabel: any = {
                    ...axisLabel,
                    [p]: {
                      ...axisLabel[p as keyof typeof axisLabel],
                      style: {
                        ...axisLabel[p as keyof typeof axisLabel]?.style,
                        translateY: value,
                      },
                    },
                  };
                  setAxisLabel(newAxisLabel);
                }}
              />
            ))}
          </div>

          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() => setAxisLabel({})}
          />

          <Divider />

          <span className="text-sm">Tick / label</span>
          {["top", "bottom", "left", "right"].map((p) => (
            <div
              key={p}
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "8px",
              }}
            >
              <CheckboxGroup
                orientation="horizontal"
                setSize="sm"
                label={p}
                value={Object.keys(ticks[p as "top" | "bottom" | "left" | "right"] ?? {})}
                onValueChange={(value) => {
                  const newTicks: ticksStateProps = {
                    ...ticks,
                    [p as "top" | "bottom" | "left" | "right"]: value.reduce(
                      (acc: any, key) => {
                        acc[key as "label" | "tick" | "line"] = true;
                        return acc;
                      },
                      {} as ticksStateProps["top"],
                    ),
                  };
                  setTicks(newTicks);
                }}
              >
                {["label", "tick", "line"].map((t) => (
                  <Checkbox
                    key={t}
                    value={t}
                    label={p + " " + t}
                  />
                ))}
              </CheckboxGroup>
            </div>
          ))}

          <Divider />

          <span className="text-sm">Tick size</span>
          <div className="grid grid-cols-2 gap-4">
            {["top", "bottom", "left", "right"].map((p) => (
              <Slider
                key={p}
                setSize="xs"
                label={p}
                value={tickSize[p as keyof typeof tickSize]}
                onChange={(value) =>
                  typeof value === "number" &&
                  setTickSize((prevTickSize) => ({
                    ...prevTickSize,
                    [p]: value,
                  }))
                }
              />
            ))}
          </div>
          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() => setTickSize({ top: 0, bottom: 0, left: 0, right: 0 })}
          />

          <Divider />

          <span className="text-sm">Tick arguments</span>
          <div className="grid grid-cols-2 gap-4">
            {["top", "bottom", "left", "right"].map((p) => (
              <Slider
                key={p}
                setSize="xs"
                label={p}
                minValue={0}
                maxValue={20}
                value={tickArguments[p as keyof typeof tickArguments]}
                onChange={(value) =>
                  typeof value === "number" &&
                  setTickArguments((prevTickArguments) => ({
                    ...prevTickArguments,
                    [p]: value,
                  }))
                }
              />
            ))}
          </div>
          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() => setTickArguments({ top: 0, bottom: 0, left: 0, right: 0 })}
          />

          <Divider />
          <Textarea
            setSize="sm"
            label="Ticks label format"
            value={ticksLabelFormat?.join("\n")}
            onValueChange={(value) => {
              const lines = value.split("\n");
              setTicksLabelFormat(lines);
            }}
          />
          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() => setTicksLabelFormat(undefined)}
          />

          <Divider />
          <Input
            label="Ticks format"
            setSize="sm"
            value={ticksFormat}
            onValueChange={(value) => setTicksFormat(value)}
          />
          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() => setTicksFormat(undefined)}
          />

          <Divider />
          <span className="text-sm">Ticks style / label /fontSize</span>
          <div className="grid grid-cols-2 gap-4">
            {["top", "bottom", "left", "right"].map((p) => (
              <Slider
                key={p}
                setSize="xs"
                label={p}
                value={ticksStyle[p as keyof typeof ticksStyle]?.label?.fontSize}
                onChange={(value) =>
                  typeof value === "number" &&
                  setTicksStyle({
                    ...ticksStyle,
                    [p]: {
                      ...ticksStyle[p as keyof typeof ticksStyle],
                      label: {
                        ...ticksStyle[p as keyof typeof ticksStyle]?.label,
                        fontSize: value,
                      },
                    },
                  })
                }
              />
            ))}
          </div>

          <Divider />
          <span className="text-sm">Ticks style / label / fill</span>

          <div className="flex flex-wrap gap-4">
            {["top", "bottom", "left", "right"].map((p) => (
              <ColorPickerSimple
                key={p}
                color={parseColor(
                  ticksStyle[p as keyof typeof ticksStyle]?.label?.fill ?? "#000000",
                ).toFormat("hsl")}
                onColorChange={(color) => {
                  const newTicksStyle: ticksStyleProps = {
                    ...ticksStyle,
                    [p]: {
                      ...ticksStyle[p as keyof typeof ticksStyle],
                      label: {
                        ...ticksStyle[p as keyof typeof ticksStyle]?.label,
                        fill: color.toString("css"),
                      },
                    },
                  };
                  setTicksStyle(newTicksStyle);
                }}
                colorSwatchProps={{
                  className: "w-6 h-6 rounded-full",
                }}
                buttonProps={{
                  text: p,
                  setSize: "sm",
                  classNames: {
                    base: "pr-1 pl-2",
                  },
                }}
              />
            ))}
          </div>

          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() =>
              setTicksStyle({
                ...ticksStyle,
                top: {
                  ...ticksStyle.top,
                  label: {
                    ...ticksStyle.top?.label,
                    fill: undefined,
                  },
                },
                bottom: {
                  ...ticksStyle.bottom,
                  label: {
                    ...ticksStyle.bottom?.label,
                    fill: undefined,
                  },
                },
                left: {
                  ...ticksStyle.left,
                  label: {
                    ...ticksStyle.left?.label,
                    fill: undefined,
                  },
                },
                right: {
                  ...ticksStyle.right,
                  label: {
                    ...ticksStyle.right?.label,
                    fill: undefined,
                  },
                },
              })
            }
          />

          <Divider />
          <span className="text-sm">Ticks style / label / translate</span>
          <div className="grid grid-cols-2 gap-4">
            {["top", "bottom", "left", "right"].map((p) => (
              <Slider
                key={p}
                fillOffset={0}
                setSize="xs"
                label={`X ${p}`}
                minValue={-100}
                maxValue={100}
                defaultValue={0}
                value={ticksStyle[p as keyof typeof ticksStyle]?.label?.translateX}
                onChange={(value) =>
                  typeof value === "number" &&
                  setTicksStyle({
                    ...ticksStyle,
                    [p]: {
                      ...ticksStyle[p as keyof typeof ticksStyle],
                      label: {
                        ...ticksStyle[p as keyof typeof ticksStyle]?.label,
                        translateX: value,
                      },
                    },
                  })
                }
              />
            ))}
            {["top", "bottom", "left", "right"].map((p) => (
              <Slider
                key={p}
                fillOffset={0}
                setSize="xs"
                label={`Y ${p}`}
                minValue={-100}
                maxValue={100}
                defaultValue={0}
                value={ticksStyle[p as keyof typeof ticksStyle]?.label?.translateY}
                onChange={(value) =>
                  typeof value === "number" &&
                  setTicksStyle({
                    ...ticksStyle,
                    [p]: {
                      ...ticksStyle[p as keyof typeof ticksStyle],
                      label: {
                        ...ticksStyle[p as keyof typeof ticksStyle]?.label,
                        translateY: value,
                      },
                    },
                  })
                }
              />
            ))}
          </div>
          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() =>
              setTicksStyle({
                ...ticksStyle,
                top: {
                  ...ticksStyle.top,
                  label: {
                    ...ticksStyle.top?.label,
                    translateX: undefined,
                    translateY: undefined,
                  },
                },
                bottom: {
                  ...ticksStyle.bottom,
                  label: {
                    ...ticksStyle.bottom?.label,
                    translateX: undefined,
                    translateY: undefined,
                  },
                },
                left: {
                  ...ticksStyle.left,
                  label: {
                    ...ticksStyle.left?.label,
                    translateX: undefined,
                    translateY: undefined,
                  },
                },
                right: {
                  ...ticksStyle.right,
                  label: {
                    ...ticksStyle.right?.label,
                    translateX: undefined,
                    translateY: undefined,
                  },
                },
              })
            }
          />

          <Divider />

          <span className="text-sm">Ticks style / label / rotate</span>
          <div className="grid grid-cols-2 gap-4">
            {["top", "bottom", "left", "right"].map((p) => (
              <Slider
                key={p}
                setSize="xs"
                label={p}
                minValue={0}
                maxValue={360}
                value={ticksStyle[p as keyof typeof ticksStyle]?.label?.rotate}
                onChange={(value) =>
                  typeof value === "number" &&
                  setTicksStyle({
                    ...ticksStyle,
                    [p]: {
                      ...ticksStyle[p as keyof typeof ticksStyle],
                      label: {
                        ...ticksStyle[p as keyof typeof ticksStyle]?.label,
                        rotate: value,
                      },
                    },
                  })
                }
              />
            ))}
          </div>
          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() => setTicksStyle({})}
          />

          <Divider />

          <span className="text-sm">Ticks style / label / textAnchor</span>
          <div className="grid grid-cols-2 gap-4">
            {["top", "bottom", "left", "right"].map((p) => (
              <NativeSelect
                classNames={{
                  label: "text-xs",
                }}
                key={p}
                label={p}
                setSize="sm"
                value={ticksStyle[p as keyof typeof ticksStyle]?.label?.textAnchor ?? "start"}
                onValueChange={(e) =>
                  setTicksStyle({
                    ...ticksStyle,
                    [p]: {
                      ...ticksStyle[p as keyof typeof ticksStyle],
                      label: {
                        ...ticksStyle[p as keyof typeof ticksStyle]?.label,
                        textAnchor: e.target.value as "start" | "middle" | "end",
                      },
                    },
                  })
                }
                options={[
                  { value: "start", label: "Start" },
                  { value: "middle", label: "Middle" },
                  { value: "end", label: "End" },
                ]}
              />
            ))}
          </div>

          <span className="text-sm">Ticks style / tick / stroke</span>
          <div className="flex flex-wrap gap-4">
            {["top", "bottom", "left", "right"].map((p) => (
              <ColorPickerSimple
                key={p}
                color={parseColor(
                  ticksStyle[p as keyof typeof ticksStyle]?.tick?.stroke ?? "#000000",
                ).toFormat("hsl")}
                onColorChange={(color) => {
                  const newTicksStyle: ticksStyleProps = {
                    ...ticksStyle,
                    [p]: {
                      ...ticksStyle[p as keyof typeof ticksStyle],
                      tick: {
                        ...ticksStyle[p as keyof typeof ticksStyle]?.tick,
                        stroke: color.toString("css"),
                      },
                    },
                  };
                  setTicksStyle(newTicksStyle);
                }}
                colorSwatchProps={{
                  className: "w-6 h-6 rounded-full",
                }}
                buttonProps={{
                  text: p,
                  setSize: "sm",
                  classNames: {
                    base: "pr-1 pl-2",
                  },
                }}
              />
            ))}
          </div>
          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() => setTicksStyle({})}
          />

          <Divider />

          <span className="text-sm">Ticks style / tick / strokeWidth</span>
          <div className="grid grid-cols-2 gap-4">
            {["top", "bottom", "left", "right"].map((p) => (
              <Slider
                key={p}
                setSize="xs"
                label={p}
                minValue={0}
                maxValue={10}
                value={ticksStyle[p as keyof typeof ticksStyle]?.tick?.strokeWidth}
                onChange={(value) =>
                  typeof value === "number" &&
                  setTicksStyle({
                    ...ticksStyle,
                    [p]: {
                      ...ticksStyle[p as keyof typeof ticksStyle],
                      tick: {
                        ...ticksStyle[p as keyof typeof ticksStyle]?.tick,
                        strokeWidth: value,
                      },
                    },
                  })
                }
              />
            ))}
          </div>

          <Divider />

          <CheckboxGroup
            orientation="horizontal"
            label="Ticks attachment"
            setSize="sm"
            value={Object.keys(ticksAttachment)}
            onValueChange={(value) => {
              setTicksAttachment(
                value.reduce((acc, key) => {
                  acc[key as "top" | "bottom" | "left" | "right"] = {
                    url: [
                      "https://picsum.photos/id/10/200/300",
                      "https://picsum.photos/id/20/200/300",
                      "https://picsum.photos/id/30/200/300",
                      "https://picsum.photos/id/40/200/300",
                      "https://picsum.photos/id/50/200/300",
                      "https://picsum.photos/id/60/200/300",
                      "https://picsum.photos/id/70/200/300",
                      "https://picsum.photos/id/80/200/300",
                      "https://picsum.photos/id/90/200/300",
                      "https://picsum.photos/id/100/200/300",
                    ],
                    width: 32,
                    height: 32,
                    offset: 32,
                  };
                  return acc;
                }, {} as ticksAttachmentProps),
              );
            }}
          >
            {["top", "bottom", "left", "right"].map((p) => (
              <Checkbox
                key={p}
                value={p}
                label={p}
              />
            ))}
          </CheckboxGroup>

          <Divider />

          <div className="grid grid-cols-2 gap-4">
            {["top", "bottom", "left", "right"].map((p) => (
              <Slider
                key={p}
                setSize="xs"
                label={`offset ${p}`}
                value={ticksAttachment[p as keyof typeof ticksAttachment]?.offset ?? 32}
                onChange={(value) => {
                  setTicksAttachment((prev) => ({
                    ...prev,
                    [p]: {
                      ...prev[p as keyof typeof prev],
                      offset: value,
                    },
                  }));
                }}
              />
            ))}
            {["top", "bottom", "left", "right"].map((p) => (
              <Slider
                key={p}
                setSize="xs"
                label={`width ${p}`}
                value={ticksAttachment[p as keyof typeof ticksAttachment]?.width ?? 32}
                onChange={(value) => {
                  setTicksAttachment((prev) => ({
                    ...prev,
                    [p]: {
                      ...prev[p as keyof typeof prev],
                      width: value,
                    },
                  }));
                }}
              />
            ))}
            {["top", "bottom", "left", "right"].map((p) => (
              <Slider
                key={p}
                setSize="xs"
                label={`height ${p}`}
                value={ticksAttachment[p as keyof typeof ticksAttachment]?.height ?? 32}
                onChange={(value) => {
                  setTicksAttachment((prev) => ({
                    ...prev,
                    [p]: {
                      ...prev[p as keyof typeof prev],
                      height: value,
                    },
                  }));
                }}
              />
            ))}
          </div>
          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() => setTicksAttachment({})}
          />
        </AccordionPanel>

        <AccordionPanel header="Grid Settings">
          <CheckboxGroup
            orientation="horizontal"
            label="Grid"
            setSize="sm"
            value={Object.keys(grid)}
            onValueChange={(value) => {
              setGrid(
                value.reduce((acc, key) => {
                  acc[key as "top" | "bottom" | "left" | "right"] = true;
                  return acc;
                }, {} as directionsBooleanProps),
              );
            }}
          >
            {["top", "bottom", "left", "right"].map((p) => (
              <Checkbox
                key={p}
                value={p}
                label={p}
              />
            ))}
          </CheckboxGroup>

          <span className="text-sm">Grid style / stroke</span>
          <div className="flex flex-wrap gap-4">
            {["top", "bottom", "left", "right"].map((p) => (
              <ColorPickerSimple
                key={p}
                color={parseColor(
                  gridStyle[p as keyof typeof gridStyle]?.stroke ?? "#000000",
                ).toFormat("hsl")}
                onColorChange={(color) => {
                  const newGridStyle: gridStyleProps = {
                    ...gridStyle,
                    [p]: {
                      ...gridStyle[p as keyof typeof gridStyle],
                      stroke: color.toString("css"),
                    },
                  };
                  setGridStyle(newGridStyle);
                }}
                colorSwatchProps={{
                  className: "w-6 h-6 rounded-full",
                }}
                buttonProps={{
                  text: p,
                  setSize: "sm",
                  classNames: {
                    base: "pr-1 pl-2",
                  },
                }}
              />
            ))}
          </div>
          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() => setGridStyle({})}
          />

          <span className="text-sm">Grid style / strokeWidth</span>
          <div className="grid grid-cols-2 gap-4">
            {["top", "bottom", "left", "right"].map((p) => (
              <Slider
                key={p}
                setSize="xs"
                label={p}
                value={gridStyle[p as keyof typeof gridStyle]?.strokeWidth}
                onChange={(value) =>
                  typeof value === "number" &&
                  setGridStyle({
                    ...gridStyle,
                    [p]: { ...gridStyle[p as keyof typeof gridStyle], strokeWidth: value },
                  })
                }
              />
            ))}
          </div>
        </AccordionPanel>

        <AccordionPanel header="Statistical Calculations">
          <Checkbox
            setSize="sm"
            label="Show calculate mean"
            isSelected={calculateMean}
            onValueChange={() => setCalculateMean(!calculateMean)}
          />
          {calculateMean && (
            <>
              <ColorPickerSimple
                color={parseColor(calculateModeStyle.stroke ?? "#000000").toFormat("hsl")}
                onColorChange={(color) =>
                  setCalculateModeStyle({ ...calculateModeStyle, stroke: color.toString("css") })
                }
                colorSwatchProps={{
                  className: "w-6 h-6 rounded-full",
                }}
                buttonProps={{
                  text: "Stroke",
                  setSize: "sm",
                  classNames: {
                    base: "pr-1 pl-2 self-start",
                  },
                }}
              />
              <Slider
                setSize="xs"
                label="Stroke width"
                minValue={0}
                maxValue={10}
                value={calculateModeStyle.strokeWidth}
                onChange={(value) =>
                  typeof value === "number" &&
                  setCalculateModeStyle({ ...calculateModeStyle, strokeWidth: value })
                }
              />
              <Input
                label="Stroke dasharray"
                setSize="sm"
                value={calculateModeStyle.strokeDasharray}
                onValueChange={(value) =>
                  setCalculateModeStyle({ ...calculateModeStyle, strokeDasharray: value })
                }
              />
            </>
          )}

          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() => setCalculateModeStyle({})}
          />

          <Divider />

          <Checkbox
            setSize="sm"
            label="Show calculate median"
            isSelected={calculateMedian}
            onValueChange={() => setCalculateMedian(!calculateMedian)}
          />
          {calculateMedian && (
            <>
              <ColorPickerSimple
                color={parseColor(calculateMedianStyle.stroke ?? "#000000").toFormat("hsl")}
                onColorChange={(color) =>
                  setCalculateMedianStyle({
                    ...calculateMedianStyle,
                    stroke: color.toString("css"),
                  })
                }
                colorSwatchProps={{
                  className: "w-6 h-6 rounded-full",
                }}
                buttonProps={{
                  text: "Stroke",
                  setSize: "sm",
                  classNames: {
                    base: "pr-1 pl-2 self-start",
                  },
                }}
              />
              <Slider
                setSize="xs"
                label="Stroke width"
                minValue={0}
                maxValue={10}
                value={calculateMedianStyle.strokeWidth}
                onChange={(value) =>
                  typeof value === "number" &&
                  setCalculateMedianStyle({ ...calculateMedianStyle, strokeWidth: value })
                }
              />
              <Input
                label="Stroke dasharray"
                setSize="sm"
                value={calculateMedianStyle.strokeDasharray}
                onValueChange={(value) =>
                  setCalculateMedianStyle({ ...calculateMedianStyle, strokeDasharray: value })
                }
              />
            </>
          )}
          <Button
            className="self-start"
            setSize="xs"
            variant="outlined"
            icon={<SvgIcon name={SvgIconName.ui.refresh12} />}
            text="Reset"
            onClick={() => setCalculateMedianStyle({})}
          />

          <Divider />
          <RadioGroup
            label="Calculate mode"
            setSize="sm"
            value={calculate.statistics}
            onValueChange={(value) =>
              setCalculate({ ...calculate, statistics: value as "mean" | "median" })
            }
          >
            <Radio
              key="mean"
              value="mean"
              label="Show Mean"
            />
            <Radio
              key="median"
              value="median"
              label="Show Median"
            />
          </RadioGroup>

          <Divider />

          <NativeSelect
            classNames={{
              label: "text-xs",
            }}
            label="Mode"
            setSize="sm"
            value={calculate.mode}
            onValueChange={(e) =>
              setCalculate({ ...calculate, mode: e.target.value as "name" | "value" })
            }
            options={[
              { value: "name", label: "Name" },
              { value: "value", label: "Value" },
            ]}
          />
        </AccordionPanel>
      </Accordion>
    </ScrollArea>
  );
};
