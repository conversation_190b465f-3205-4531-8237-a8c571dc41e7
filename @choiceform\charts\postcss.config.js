/* eslint-env node */
const postcssPresetEnvOptions = {
  features: {
    'is-pseudo-class': { preserve: true },
    'nesting-rules': false,
  },
};

// /**
//  * Note: If your postcss.config.js needs to support other non-Next.js tools in
//  * the same project, you must use the interoperable object-based format instead
//  */
module.exports = {
  plugins: {
    'postcss-import': {},
    'postcss-nested-ancestors': {},
    'tailwindcss/nesting': 'postcss-nested',
    tailwindcss: {},
    'postcss-preset-env': postcssPresetEnvOptions,
  },
};
// module.exports = {
//   plugins: {
//     'postcss-import': {},
//     'tailwindcss/nesting': {},
//     tailwindcss: {},
//     autoprefixer: {},
//   },
// }

