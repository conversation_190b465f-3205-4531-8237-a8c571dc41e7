import { useRef, useEffect } from "react";

type ConicGradientCanvasProps = {
  className?: string;
  style?: React.CSSProperties;
  id?: string;
  color?: string[];
  width: number;
  height: number;
  placement?: "top" | "bottom" | "left" | "right";
};

export const ConicGradientCanvas = ({
  className,
  style,
  id,
  color = ["white", "black"],
  width = 200,
  height = 200,
  placement = "top",
}: ConicGradientCanvasProps) => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);

  useEffect(() => {
    const placementMap = () => {
      switch (placement) {
        case "top":
          return -Math.PI / 2;
        case "bottom":
          return Math.PI / 2;
        case "left":
          return Math.PI;
        case "right":
          return 0;
      }
    };
    if (canvasRef.current) {
      const ctx = canvasRef.current.getContext("2d");
      if (ctx) {
        ctx.save(); // 保存当前状态
        ctx.translate(width / 2, height / 2); // 将原点移动到中心
        ctx.rotate(placementMap()); // 旋转画布
        ctx.translate(-width / 2, -height / 2); // 将原点向后移动
        const gradient = ctx.createConicGradient(0, width / 2, height / 2);

        color.forEach((c, i) => {
          gradient.addColorStop(i / (color.length - 1), c);
        });

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, height);
        ctx.restore(); // 恢复原状
      }
    }
  }, [color, width, height, placement]);

  return (
    <canvas
      className={className}
      style={style}
      id={id}
      ref={canvasRef}
      width={width}
      height={height}
    />
  );
};
