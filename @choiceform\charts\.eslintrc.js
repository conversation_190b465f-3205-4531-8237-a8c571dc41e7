/* eslint-env node */

/**
 * @type {import('eslint').ESLint.ConfigData}
 */
module.exports = {
  extends: ['@choiceform/eslint-config/react'],
  root: true,
  overrides: [
    // the keyword `assert` and `import` can not be process correctly by eslint yet.
    // {
    //   files: ['*.@(js|cjs|mjs)'],
    //   extends: ['plugin:node/recommended'],
    // },
    {
      files: ['src/**/*.ts?(x)'],
      rules: {
        'prefer-const': 'off',
        'react/prop-types': 'off',
        'max-depth': 'off',
        '@typescript-eslint/no-unused-vars': 'off',
        '@typescript-eslint/no-explicit-any': 'off',
        '@typescript-eslint/no-empty-interface': 'off',
      },
    },
  ],
};
