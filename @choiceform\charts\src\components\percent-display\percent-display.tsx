import { ReactNode, useEffect, useState } from "react";
import { HTMLProps } from "@choiceform/ui-react";

interface PercentDisplayProps {
  className?: string;
  targetPercent: number;
  unit?: string;
  duration?: number;
  onStart?: boolean;
  topLabel?: ReactNode;
  bottomLabel?: ReactNode;
  currentPercentProps?: HTMLProps<"strong">;
}

export const PercentDisplay = ({
  className,
  targetPercent,
  unit = "%",
  onStart = true,
  duration = 1000,
  topLabel,
  bottomLabel,
  currentPercentProps,
}: PercentDisplayProps) => {
  const [currentPercent, setCurrentPercent] = useState(0);

  useEffect(() => {
    if (!onStart) {
      return;
    }

    const startTime = Date.now();

    const update = () => {
      const elapsedTime = Date.now() - startTime;
      const newPercent = Math.min((elapsedTime / duration) * targetPercent, targetPercent);

      setCurrentPercent(Math.round(newPercent));

      if (newPercent < targetPercent) {
        requestAnimationFrame(update);
      }
    };

    update();
  }, [targetPercent, duration, onStart]);

  return (
    <div className={className}>
      {topLabel}
      <strong {...currentPercentProps}>
        {`${currentPercent}`}
        {unit}
      </strong>
      {bottomLabel}
    </div>
  );
};
