import * as d3 from "d3";
import React, { useEffect, useRef } from "react";
import { ConicGradientCanvas } from "../conic-gradient-canvas";
import { ArcData, appendDot } from "./circular-progress-dot";
import { appendDynamicDot } from "./circular-progress-dynamic-dot";

export interface Segment {
  percent: number;
  gradient?: string[];
  arcStyle?: {
    fill?: string;
    fillOpacity?: number;
    stroke?: string;
    strokeWidth?: number;
    strokeOpacity?: number;
  };
  dot?: {
    position: "start" | "start-alien" | "end" | "end-alien" | "center" | "dynamic";
    duration?: number;
    delay?: number;
  };
  dotStyle?: {
    size?: number;
    fill?: string;
    fillOpacity?: number;
    stroke?: string;
    strokeWidth?: number;
    strokeOpacity?: number;
  };
}

export interface Segments {
  gradient?: string[];
  backgroundColor?: {
    fill?: string;
    fillOpacity?: number;
  };
  arc: Segment[];
}

type CircularProgressBarProps = {
  size?: number;
  arcWidth?: number;
  segments: Segments[];
  gap?: number;
  cornerRadius: number;
  minAngle: number;
  maxAngle: number;
  backgroundArcOffset?: number;
  backgroundColor?: string;
  animate?: boolean;
  onStart?: boolean;
  duration?: number;
  separateArcs?: boolean;
  separateArcsGap?: number;
  gradientPlacement?: "top" | "bottom" | "left" | "right";
  dotDuration?: number;
  children?: (
    duration: number,
    segments: Segments[],
    arcAnimationStatus: Record<string, boolean>,
  ) => React.ReactNode;
  setHoveredState?: (d: Segments | null) => void;
};

export const CircularProgressBar = ({
  children,
  size = 200,
  arcWidth = 10,
  segments,
  gap = 0,
  cornerRadius,
  minAngle,
  maxAngle,
  backgroundArcOffset = 0,
  backgroundColor = "none",
  animate,
  onStart = true,
  duration = 1000,
  separateArcs = false,
  separateArcsGap = 10,
  gradientPlacement = "top",
  dotDuration = 500,
  setHoveredState,
}: CircularProgressBarProps) => {
  const ref = useRef<SVGSVGElement | null>(null);
  const [arcAnimationStatus, setArcAnimationStatus] = React.useState<Record<string, boolean>>({});

  useEffect(() => {
    if (ref.current) {
      const svg = d3.select(ref.current);
      svg.selectAll("*").remove(); // Clear the svg content before adding new elements

      const outerRadius = Math.min(size, size) / 2 - arcWidth;
      const innerRadius = outerRadius - arcWidth;

      // Convert angles from degrees to radians and adjust for D3's angle system
      const startAngle = (minAngle / 180) * Math.PI;
      const endAngle = (maxAngle / 180) * Math.PI;

      const g = svg.append("g").attr("transform", `translate(${size / 2}, ${size / 2})`);

      const path = d3.arc<Segment>().cornerRadius(cornerRadius);
      const background = d3.arc().cornerRadius(cornerRadius);

      const pie = d3
        .pie<Segment>()
        .value((d) => d.percent)
        .sort(null)
        .startAngle(startAngle)
        .endAngle(endAngle)
        .padAngle(((gap / 2) * Math.PI) / 180);

      segments.forEach((segmentGroup, groupIndex) => {
        let cumulativeDelay = 0;
        // Calculate the innerRadius and outerRadius based on the separateArcs prop
        const newInnerRadius = separateArcs
          ? innerRadius - groupIndex * arcWidth - groupIndex * separateArcsGap
          : innerRadius;
        const newOuterRadius = separateArcs
          ? outerRadius - groupIndex * arcWidth - groupIndex * separateArcsGap
          : outerRadius;

        const canvas = document.getElementById(`canvas-${groupIndex}`);
        const dataUrl = (canvas as HTMLCanvasElement).toDataURL();

        const defs = svg.append("defs");

        const pattern = defs
          .append("pattern")
          .attr("id", `pattern-${groupIndex}`)
          .attr("patternUnits", "userSpaceOnUse")
          .attr("patternTransform", `translate(${size / 2}, ${size / 2})`)
          .attr("width", size)
          .attr("height", size);

        pattern
          .append("image")
          .attr("xlink:href", dataUrl)
          .attr("preserveAspectRatio", "xMidYMid slice")
          .attr("id", `image-${groupIndex}`)
          .attr("href", dataUrl)
          .attr("x", 0)
          .attr("y", 0)
          .attr("width", size)
          .attr("height", size);

        const fill = segmentGroup.gradient ? `url(#pattern-${groupIndex})` : "none";

        background
          .innerRadius(newInnerRadius - backgroundArcOffset)
          .outerRadius(newOuterRadius + backgroundArcOffset);
        g.selectAll(`.arc-background-${groupIndex}`)
          .data(pie(segmentGroup.backgroundColor ? [{ percent: 100 }] : []))
          .enter()
          .append("path")
          .attr("d", background as any)
          .attr("fill", segmentGroup.backgroundColor?.fill ?? "none")
          .attr("fill-opacity", segmentGroup.backgroundColor?.fillOpacity ?? 1);

        path.innerRadius(newInnerRadius).outerRadius(newOuterRadius);
        g.selectAll(`.arc-group-${groupIndex}`)
          .data(pie(segmentGroup.arc))
          .enter()
          .append("path")
          .attr("class", `arc-group-${groupIndex}`)
          .attr("d", path as any)
          .attr("fill", (d) => d.data.arcStyle?.fill ?? fill)
          .attr("fill-opacity", (d) => d.data.arcStyle?.fillOpacity ?? 1)
          .attr("stroke", (d) => d.data.arcStyle?.stroke ?? "none")
          .attr("stroke-width", (d) => d.data.arcStyle?.strokeWidth ?? 0)
          .attr("opacity", animate ? 0 : 1)
          .on("mouseover", function () {
            if (setHoveredState) {
              setHoveredState(segmentGroup);
            }
          })
          .on("mouseout", function () {
            if (setHoveredState) {
              setHoveredState(null);
            }
          })
          .each(function (d) {
            if (onStart && animate) {
              const arc = d3.select(this);
              const arcTransition = arc
                .transition()
                .duration(duration)
                .delay(cumulativeDelay + groupIndex * duration)
                .attrTween("d", function (d: any) {
                  const interpolate = d3.interpolate(
                    { ...d, startAngle: d.startAngle, endAngle: d.startAngle },
                    { ...d, innerRadius: newInnerRadius, outerRadius: newOuterRadius },
                  );
                  return function (t: number): string {
                    const interpolated = interpolate(t);
                    path
                      .innerRadius(interpolated.innerRadius)
                      .outerRadius(interpolated.outerRadius);
                    return path(interpolated) ?? "";
                  };
                })
                .style("opacity", 1)
                .on("end", () => {
                  setArcAnimationStatus((prevStatus) => ({
                    ...prevStatus,
                    [d.data.percent]: true,
                  }));
                });
              if (d.data.dot?.position === "dynamic") {
                appendDynamicDot(
                  d as ArcData,
                  g,
                  newInnerRadius,
                  arcWidth,
                  duration,
                  cumulativeDelay + groupIndex * duration,
                  animate,
                );
              }
              arcTransition.end().then(() => {
                if (d.data.dot?.position !== "dynamic") {
                  appendDot(d as ArcData, g, newInnerRadius, arcWidth, gap, animate);
                }
              });
              cumulativeDelay += duration;
            } else {
              if (d.data.dot) {
                appendDot(d as ArcData, g, newInnerRadius, arcWidth, gap, animate);
              }
            }
          });
      });
    }
  }, [
    animate,
    arcWidth,
    backgroundArcOffset,
    backgroundColor,
    cornerRadius,
    dotDuration,
    duration,
    gap,
    maxAngle,
    minAngle,
    onStart,
    segments,
    separateArcs,
    separateArcsGap,
    setHoveredState,
    size,
  ]);

  return (
    <div>
      <svg
        ref={ref}
        width={size}
        height={size}
      />
      {children?.(duration, segments, arcAnimationStatus)}
      {segments.map((segmentGroup, i) => (
        <ConicGradientCanvas
          key={i}
          style={{ display: "none" }}
          id={`canvas-${i}`}
          color={segmentGroup.gradient}
          width={size / 4}
          height={size / 4}
          placement={gradientPlacement}
        />
      ))}
    </div>
  );
};
