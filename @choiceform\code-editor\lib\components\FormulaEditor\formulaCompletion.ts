import {
  Completion,
  CompletionContext,
  CompletionResult,
  autocompletion,
  snippetCompletion,
} from "@codemirror/autocomplete";
import { EditorView } from "@codemirror/view";
import { useMemo } from "react";
import { <PERSON><PERSON>ontroller } from "../../code-controller";
import { CompletionSource } from "../../completion/completion";
import { FormulaEditorProps } from "../../types";
import { Extension } from "@codemirror/state";

// abstract class CompletionSource {
//   constructor() {
//     this.completionSource = this.completionSource.bind(this);
//   }

//   abstract completionSource(
//     context: CompletionContext
//   ): CompletionResult | Promise<CompletionResult | null> | null;
// }

class FormulaCompletion extends CompletionSource {
  completionSource(
    context: CompletionContext,
  ): CompletionResult | Promise<CompletionResult | null> | null {
    const matchPath = context.matchBefore(/\w+/);
    if (!matchPath) {
      return null;
    }
    const json = (this.completionController.props as FormulaEditorProps).formulaData ?? [];
    const options = json.map((data) => {
      if (data.entity) {
        return snippetCompletion(data.entity, {
          type: data.type,
          label: data.name,
          detail: data.type,
          info: data.doc,
        });
      } else {
        return {
          type: data.type,
          label: data.name,
          detail: data.type,
          info: data.doc,
        };
      }
    });
    const completions = {
      from: matchPath.from,
      validFor: /^\w*$/,
      options,
    };

    return completions;
  }
}

class FieldCompletion extends CompletionSource {
  completionSource(
    context: CompletionContext,
  ): CompletionResult | Promise<CompletionResult | null> | null {
    const matchPath = context.matchBefore(/[\w|\u4e00-\u9fa5]*/);

    if (!matchPath) {
      return null;
    }
    const json = (this.completionController.props as FormulaEditorProps).fieldData ?? [];
    const options = json.map((data) => {
      const insert = `\${{${JSON.stringify(data)}}}$`;
      return {
        type: "field",
        label: data.name,
        detail: "field",
        apply: (view: EditorView, c: Completion, from: number, to: number) => {
          view.dispatch({
            changes: {
              from: from,
              to: to,
              insert: insert,
            },
          });
        },
      };
    });

    const completions = {
      from: matchPath.from,
      validFor: /^\w*$/,
      options,
    };

    return completions;
  }
}

export const useFormulaCompletion = (props: FormulaEditorProps): Extension => {
  const { fieldData } = props;
  const codeController = useMemo(() => new CodeController<FormulaEditorProps>(), []);
  useMemo(() => {
    codeController.setCompleteSource(fieldData, "", "Text", false, props);
  }, [codeController, fieldData, props]);

  const FormulaSource = useMemo(() => new FormulaCompletion(), []);
  const exposingSource = useMemo(() => new FieldCompletion(), []);

  useMemo(() => {
    FormulaSource.completionController = codeController;
    exposingSource.completionController = codeController;
  }, [codeController, exposingSource, FormulaSource]);

  const completionSources = useMemo(() => {
    return autocompletion({
      override: [FormulaSource.completionSource, exposingSource.completionSource],
    });
  }, [FormulaSource.completionSource, exposingSource.completionSource]);
  return completionSources;
};
