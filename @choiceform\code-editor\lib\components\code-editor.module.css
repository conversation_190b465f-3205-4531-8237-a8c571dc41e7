.tx-code {
  &[data-disabled="true"] {
    & :global(.cm-editor),
    & :global(.cm-editor *) {
      @apply pointer-events-none bg-transparent text-secondary;
    }
  }

  & span[contenteditable="false"] {
    /* 导致 wf 无法点击 tag  */
    /* @apply pointer-events-none; */
  }

  & :global(.cm-theme-light) {
    flex-grow: 1;
  }

  & :global(.cm-scroller) {
    overflow: unset;
    @apply focus:outline-none;
  }

  & :global(.cm-editor) {
    outline: none;
    &:global(.cm-focused) {
      @apply outline-none;
    }
  }

  &.tx-code__editor {
    width: 100%;
    & :global(.cm-scroller) {
      font-family: var(--font-monospace);
    }

    &[data-size="xs"] {
      & :global(.cm-content) {
        @apply text-xs;
        padding: 0.375rem 0;
      }
    }

    &[data-size="sm"] {
      & :global(.cm-content) {
        @apply text-sm;
        padding: 0.563rem 0;
      }
    }

    &[data-size="md"] {
      & :global(.cm-content) {
        padding: 0.75rem 0;
      }
    }

    &[data-size="lg"] {
      & :global(.cm-content) {
        padding: 0.938rem 0;
      }
    }
  }

  &.tx-code__input {
    & :global(.cm-scroller) {
      font-family: var(--font-en-us);
      @apply min-h-inherit;
    }

    & :global(.cm-editor) {
      @apply bg-transparent;
    }

    & :global(.cm-content) {
      @apply min-w-0 p-0;
    }

    & :global(.cm-line) {
      @apply p-0;

      & > :global(.cm-widgetBuffer:first-child + span > div[role="status"]) {
        @apply ml-px;
      }
    }
  }

  &.tx-code__formula {
    & :global(.cm-scroller) {
      font-family: var(--font-en-us);
    }

    & :global(.cm-editor) {
      @apply bg-transparent;
    }

    & :global(.cm-content) {
      @apply p-0;
    }

    & :global(.cm-line) {
      @apply p-0;
    }
  }

  & :global(.cm-default-quote) {
    @apply inline-flex items-center truncate;

    &:not([data-contrast]) {
      @apply bg-light-200;
    }
  }

  &[data-size="xs"] {
    & :global(.cm-default-quote) {
      height: calc(1.125rem - 2px);
      border-radius: 0.125rem;
      padding: 0 0.25rem;
    }
  }

  &[data-size="sm"] {
    & :global(.cm-default-quote) {
      height: calc(1.375rem - 2px);
      border-radius: 0.188rem;
      padding: 0 0.313rem;
    }
  }

  &[data-size="md"] {
    & :global(.cm-default-quote) {
      height: calc(1.625rem - 2px);
      border-radius: 0.25rem;
      padding: 0 0.375rem;
    }
  }

  &[data-size="lg"] {
    & :global(.cm-default-quote) {
      height: calc(1.875rem - 2px);
      border-radius: 0.313rem;
      padding: 0 0.438rem;
    }
  }

  & :global(.cm-function) {
    color: #9d8911;
  }

  & :global(.cm-keyword) {
    color: #f47067;
  }
}
