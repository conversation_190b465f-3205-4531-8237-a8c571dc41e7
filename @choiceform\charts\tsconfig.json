{"extends": "@choiceform/typescript-config/react-library.json", "compilerOptions": {"outDir": "dist", "baseUrl": ".", "rootDir": "src", "paths": {"ui*": ["./node_modules/@choiceform/ui-react/src/*"], "interface/*": ["./src/*"]}, "types": ["node", "react", "react-dom"], "incremental": false, "plugins": [{"name": "typescript-plugin-css-modules"}], "noImplicitAny": true, "noUnusedLocals": true, "noUnusedParameters": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.d.ts"], "exclude": ["node_modules", "dist"]}