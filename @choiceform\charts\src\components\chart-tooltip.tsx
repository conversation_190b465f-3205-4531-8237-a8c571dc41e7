export interface ChartTooltipProps {
  children?: React.ReactNode;
  x: number;
  y: number;
}

export const ChartTooltip = ({ children, x, y }: ChartTooltipProps) => {
  return (
    <div
      style={{
        position: "absolute",
        top: y,
        left: x,
        background: "#333",
        color: "#fff",
        padding: "10px",
        borderRadius: "4px",
        pointerEvents: "none",
      }}
    >
      {children}
    </div>
  );
};
