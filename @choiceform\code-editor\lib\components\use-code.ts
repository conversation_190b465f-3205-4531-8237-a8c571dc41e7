import {
  <PERSON>p<PERSON><PERSON><PERSON>,
  ReactRef,
  SlotsToClasses,
  tcx,
  useDOMRef,
  useFea,
  type ScrollAreaProps,
  type UseFeaProps,
} from "@choiceform/ui-react";
import { ReactCodeMirrorProps } from "@uiw/react-codemirror";
import { ReactNode, useCallback, useMemo } from "react";
import { mergeProps } from "react-aria";
import styles from "./code-editor.module.css";
import { CodeInputVariant, type CodeInputSlots, type CodeInputVariantProps } from "./code.tv";

interface Props extends CodeInputVariantProps, UseFeaProps {
  classNames?: SlotsToClasses<CodeInputSlots>;
  ref?: ReactRef<HTMLDivElement>;
  label?: ReactNode | string;
  description?: ReactNode | string;
  errorMessage?: ReactNode | string;
  scrollAreaProps?: ScrollAreaProps;
  editorType?: "editor" | "input" | "formula";
}

export interface UseCodeProps
  extends Props,
    Omit<ReactCodeMirrorProps, "defaultValue" | "onChange" | "value"> {}

export function useCodeCore(props: UseCodeProps) {
  const {
    ref,
    label,
    autoFocus,
    className,
    classNames,
    disableFocus,
    isDisabled,
    isReadOnly,
    isRequired,
    isInvalid,
    setSize = "md",
    scrollAreaProps,
    editorType = "editor",
    variant,
    intent,
    ...rest
  } = props;

  const domRef = useDOMRef(ref);

  const slots = useMemo(
    () =>
      CodeInputVariant({
        setSize: props.setSize,
        editorType,
      }),
    [props.setSize, editorType],
  );

  const { getFeaProps } = useFea({
    ...rest,
    autoFocus,
    isDisabled,
    isReadOnly,
    isInvalid,
    variant,
    intent,
    // className: tcx(slots.inputWrapper({ class: classNames?.inputWrapper }), className),
    className: tcx(slots.inputWrapper(), className),
  });

  const getBaseProps: PropGetter = useCallback(
    (props = {}) => {
      return {
        ref: domRef,
        className: tcx(
          styles["tx-code"],
          styles[`tx-code__${editorType}`],
          //slots.base({ class: classNames?.base }),
          slots.base(),
          className,
        ),
        "data-slot": "base",
        "data-disabled": isDisabled,
        "data-invalid": isInvalid,
        "data-readonly": isReadOnly,
        "data-required": isRequired,
        "data-has-label": !!label,
        "data-size": setSize,
        ...props,
      };
    },
    [
      className,
      classNames?.base,
      domRef,
      editorType,
      isDisabled,
      isInvalid,
      isReadOnly,
      isRequired,
      label,
      setSize,
      slots,
    ],
  );

  const getScrollAreaProps: PropGetter = useCallback(
    (props = {}) => {
      return {
        ...mergeProps(getFeaProps(props), scrollAreaProps, props),
        "data-slot": "scrollArea",
        setSize: "sm",
        classNames: {
          viewport: slots.scrollAreaViewport(),
          // viewport: slots.scrollAreaViewport({ class: classNames?.scrollAreaViewport }),
          content: slots.scrollAreaContent(),
          // content: slots.scrollAreaContent({ class: classNames?.scrollAreaContent }),
        },
      };
    },
    [
      classNames?.scrollAreaContent,
      classNames?.scrollAreaViewport,
      getFeaProps,
      scrollAreaProps,
      slots,
    ],
  );

  const getLabelProps: PropGetter = useCallback(
    (props = {}) => {
      return {
        ...props,
        // className: slots.label({ class: classNames?.label }),
        className: slots.label(),
        "data-slot": "label",
      };
    },
    [slots, classNames?.label],
  );

  const getDescriptionProps: PropGetter = useCallback(
    (props = {}) => {
      return {
        ...props,
        // className: slots.description({ class: classNames?.description }),
        className: slots.description(),
        "data-slot": "description",
      };
    },
    [slots, classNames?.description],
  );

  const getMessageProps: PropGetter = useCallback(
    (props = {}) => {
      return {
        ...props,
        // className: slots.errorMessage({ class: classNames?.errorMessage }),
        className: slots.errorMessage(),
        "data-slot": "errorMessage",
      };
    },
    [slots, classNames?.errorMessage],
  );

  return {
    label: props.label,
    description: props.description,
    errorMessage: props.errorMessage,
    getBaseProps,
    getScrollAreaProps,
    getLabelProps,
    getDescriptionProps,
    getMessageProps,
  };
}
