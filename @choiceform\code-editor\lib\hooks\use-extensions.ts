import { useMemo } from "react";
import { getExtensions } from "../extensions-config";
import type { CodeEditorProps } from "../types";
import { useAutocompletionExtension } from "./use-auto-completion";

// interface UseExtensionsArgs {
//   basicSetup: CodeEditorProps['basicSetup'];
//   extensions: CodeEditorProps['extensions'];
//   extensionConfig?: CodeEditorProps['expandConfig'];
//   exposingData?: CodeEditorProps['exposingData'];
//   codeType?: CodeEditorProps['codeType'];
//   preview?: CodeEditorProps['preview'];
// }

export function useExtensions(props: CodeEditorProps) {
  const { basicSetup = false, extensionConfig, extensions = [] } = props;
  const autocompletionExtension = useAutocompletionExtension(props);
  // const codeController = useCodeController();

  const extensionsConfig = useMemo(
    () => getExtensions(extensionConfig, extensions),
    [extensionConfig, extensions],
  );

  const finaleExtensions = useMemo(
    () => [extensionsConfig, autocompletionExtension],
    [extensionsConfig, autocompletionExtension],
  );

  return { basicSetup, extensions: finaleExtensions };
}
