import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import dts from "vite-plugin-dts";
// import { resolve } from "path";
// import { visualizer } from "rollup-plugin-visualizer";
import peerDepsExternal from "rollup-plugin-peer-deps-external";
import tsconfigPaths from "vite-tsconfig-paths";

// https://vitejs.dev/config/
export default defineConfig({
  mode: "production",
  build: {
    lib: {
      // Could also be a dictionary or array of multiple entry points
      entry: "./src/index.ts",
      // the proper extensions will be added
      name: "index",
      fileName: "index",
      formats: ["es"],
    },
  },
  plugins: [
    tsconfigPaths(),
    react(),
    // typescript(),
    dts({ rollupTypes: true }),
    // visualizer({ emitFile: false, open: true }) as any,
    peerDepsExternal(),
  ],
});
