/* eslint-disable max-depth */
import _ from "lodash";
import { CompletionContext } from "@codemirror/autocomplete";
import { ReactNode } from "react";
import { evalFunc, evalScript } from "./eval/evalScript";
import { CodeInputValue, CodeType } from "../types";

export type CompInfo = {
  name: string;
  type: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: Record<string, any>;
  dataDesc: Record<string, ReactNode>;
};

export function obj2CodeInputString(input: CodeInputValue | undefined): string | undefined {
  if (typeof input === "string" || input === undefined) {
    return input;
  }
  try {
    let res = "";
    input.forEach((value) => {
      if (typeof value === "string") {
        res += value;
      } else {
        res += `\${{${JSON.stringify(value)}}}$`;
      }
    });
    return res;
  } catch (error) {
    return String(input);
  }
}

export function string2CodeInputObj(input: string) {
  const segments: string[] = [];
  let position = 0;
  let start = input.indexOf("${{");
  while (start >= 0) {
    let i = start + 3;
    while (i < input.length && input[i] === "{") i++;
    let end = input.indexOf("}}$", i);
    if (end < 0) {
      break;
    }
    const nextStart = input.indexOf("${{", end + 3);
    const maxIndex = nextStart >= 0 ? nextStart : input.length;
    const maxStartOffset = i - start - 3;
    let sum = i - start;
    let minValue = Number.MAX_VALUE;
    let minOffset = Number.MAX_VALUE;
    for (; i < maxIndex; i++) {
      if (input[i] === "{" || (input[i] === "$" && input[i + 1] === "{")) {
        sum++;
      } else if (input[i] === "}" || (input[i] === "$" && input[i - 1] === "}")) {
        sum--;
        if (input[i - 1] === "}") {
          const offset = Math.min(Math.max(sum, 0), maxStartOffset);
          const value = Math.abs(sum - offset);
          if (value < minValue || (value === minValue && offset < minOffset)) {
            minValue = value;
            minOffset = offset;
            end = i + 1;
          }
        }
      }
    }
    const left = input.slice(position, start + minOffset);
    let right = input.slice(start + minOffset + 3, end - 3);
    try {
      right = JSON.parse(right);
    } catch (error) {
      // do nothing
    }
    segments.push(left, right);
    position = end;
    start = nextStart;
  }
  segments.push(input.slice(position));
  return segments.filter(function (t) {
    return t;
  });
}

export function getDynamicStringSegments(input: string): string[] {
  const segments: string[] = [];
  let position = 0;
  let start = input.indexOf("{{");
  while (start >= 0) {
    let i = start + 2;
    while (i < input.length && input[i] === "{") i++;
    let end = input.indexOf("}}", i);
    if (end < 0) {
      break;
    }
    const nextStart = input.indexOf("{{", end + 2);
    const maxIndex = nextStart >= 0 ? nextStart : input.length;
    const maxStartOffset = i - start - 2;
    let sum = i - start;
    let minValue = Number.MAX_VALUE;
    let minOffset = Number.MAX_VALUE;
    for (; i < maxIndex; i++) {
      switch (input[i]) {
        case "{":
          sum++;
          break;
        case "}":
          sum--;
          if (input[i - 1] === "}") {
            const offset = Math.min(Math.max(sum, 0), maxStartOffset);
            const value = Math.abs(sum - offset);
            if (value < minValue || (value === minValue && offset < minOffset)) {
              minValue = value;
              minOffset = offset;
              end = i + 1;
            }
          }
          break;
      }
    }
    segments.push(input.slice(position, start + minOffset), input.slice(start + minOffset, end));
    position = end;
    start = nextStart;
  }
  segments.push(input.slice(position));
  return segments.filter((t) => t);
}

export function checkCursorInBinding(context: CompletionContext, isFunction?: boolean): boolean {
  if (isFunction) {
    return true;
  }
  const { state, pos } = context;
  const doc = state.sliceDoc(0, pos);
  const segments = getDynamicStringSegments(doc);
  let cumCharCount = 0;
  for (const segment of segments) {
    const start = cumCharCount;
    const dynamicStart = segment.indexOf("{{");
    const dynamicDoesStart = dynamicStart > -1;
    const dynamicStartIdx = dynamicStart + start + 2;

    const dynamicEnd = segment.indexOf("}}");
    const dynamicDoesEnd = dynamicEnd > -1;
    const dynamicEndIdx = dynamicEnd + start;

    if (dynamicDoesStart && dynamicStartIdx <= pos && (!dynamicDoesEnd || pos <= dynamicEndIdx)) {
      return true;
    }

    cumCharCount += segment.length;
  }
  return false;
}

export function transformCompInfoIntoRecord(compInfo: Array<CompInfo>): Record<string, unknown> {
  return _.fromPairs(compInfo.map((info) => [info.name, info.data]));
}

export const obj2CodeString = (input?: Record<string, unknown>) => {
  let result = "";
  if (typeof input === "object") {
    for (const key in input) {
      const temp = `var ${key} = ${JSON.stringify(input[key])};`;
      result += temp;
    }
  }
  return result;
};

export function getDynamicStringResult(
  input?: string,
  context?: any,
  codeString?: string,
  codeType: CodeType = "Function",
) {
  const data = {
    haveError: false,
    result: "",
  };
  if (!input) {
    return data;
  }

  if (codeType === "Function") {
    try {
      const value = evalFunc(input, context, codeString);
      data.result = value;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      data.haveError = true;
      data.result = error.message;
    }
  } else {
    const segments: string[] = [];
    const reg = /\{\{(.+?)\}\}/g;

    const result = input.replace(reg, function () {
      // eslint-disable-next-line prefer-rest-params
      segments.push(arguments[1]);
      try {
        // eslint-disable-next-line prefer-rest-params
        const value = evalScript(arguments[1], context, codeString);
        // const value = (0, eval)(context + arguments[1]);
        // if (typeof value === 'string') {
        //   return `"${value}"`;
        // }
        return value;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (error: any) {
        data.haveError = true;
        return `"${error.message}"`;
      }
    });

    data.result = result;
  }

  return data;
}
