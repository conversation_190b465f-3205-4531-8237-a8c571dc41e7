import { Expandable, Popover, ScrollArea, useMergedValue } from "@choiceform/ui-react";
import { useEffect, useMemo, useState } from "react";
import { CodeController } from "../../code-controller";
import { useDefaultProps } from "../../hooks/use-default-props";
import { CodeEditorProps } from "../../types";
import { CodeControlContext } from "../context";
import { useCodeCore } from "../use-code";
import { ChoiceformCodeMirror } from "./CodeCore";

const CodeCommon = (props: CodeEditorProps) => {
  const {
    classNames,
    autoFocus,
    codeType,
    completeString,
    disableFocus,
    enableJsServer,
    exitFullscreenTooltip = "Exit fullscreen",
    expandableProps,
    expandable,
    expandedTitle,
    exposingData,
    fullscreenTooltip = "Fullscreen",
    intent,
    isDisabled,
    isInvalid,
    isReadOnly,
    lowCode,
    onChange,
    onError,
    popover,
    preview,
    setSize,
    value,
    variant,
    ...rest
  } = props;

  const {
    label,
    description,
    errorMessage,
    getBaseProps,
    getScrollAreaProps,
    getLabelProps,
    getDescriptionProps,
    getMessageProps,
  } = useCodeCore({
    classNames,
    editorType: "editor",
    setSize,
    isDisabled,
    isReadOnly,
    isInvalid,
    variant,
    intent,
  });

  const [innerValue, setValue] = useMergedValue({
    defaultValue: value,
    onChange,
  });

  const [previewValue, setPreviewValue] = useState("");
  const [innerError, setError] = useMergedValue({ value: props.isInvalid, onChange: onError });

  // const [flag, setFlag] = useState(false);
  const isPopoverEnabled = !!popover;
  const popoverConfig = typeof popover === "object" ? popover : {};
  const codeController = useMemo(() => new CodeController<CodeEditorProps>(), []);

  useMemo(() => {
    codeController.setCompleteSource(
      exposingData,
      completeString,
      codeType,
      enableJsServer,
      props,
    );
  }, [codeController, codeType, completeString, enableJsServer, exposingData, props]);

  useEffect(() => {
    if (lowCode && props.isInvalid !== true) {
      value && codeController.eval(value);
      setError(!codeController.status);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <CodeControlContext.Provider value={codeController}>
      <Popover
        content={
          <ChoiceformCodeMirror
            editable={false}
            maxHeight="140px"
            value={previewValue}
            extensionConfig={{ lineNumbers: { enabled: false } }}
          />
        }
        isDisabled={!isPopoverEnabled}
        placement="bottom"
        className="p-1"
        open={!!previewValue}
        matchTriggerWidth
        offset={2}
        trigger="focus"
        {...popoverConfig}
      >
        <div {...getBaseProps()}>
          {label && <label {...getLabelProps()}>{label}</label>}
          <ScrollArea {...getScrollAreaProps()}>
            <Expandable
              title={expandedTitle}
              setSize={props.setSize}
              expandable={expandable}
              fullscreenTooltip={fullscreenTooltip}
              exitFullscreenTooltip={exitFullscreenTooltip}
              hoverVisiable
              {...expandableProps}
              collapsed={
                <ChoiceformCodeMirror
                  {...rest}
                  editable={!props.isReadOnly && !props.isDisabled}
                  value={innerValue}
                  onFocus={() => {
                    if (lowCode) {
                      const value = codeController.eval(innerValue);
                      isPopoverEnabled && setPreviewValue(value);
                      preview?.(value);
                      setError(!codeController.status);
                    }
                  }}
                  onChange={(...values) => {
                    setValue(values[0]);
                    if (lowCode) {
                      const value = codeController.eval(values[0]);
                      isPopoverEnabled && setPreviewValue(value);
                      preview?.(value);
                      setError(!codeController.status);
                    }
                  }}
                  onBlur={(e) => {
                    isPopoverEnabled && setPreviewValue("");
                    rest.onBlur?.(e);
                  }}
                />
              }
              expanded={
                <CodeEditor
                  {...props}
                  className="flex-grow h-full w-full"
                  scrollAreaProps={{
                    classNames: {
                      viewport: "h-auto",
                    },
                  }}
                  minWidth={"100%"}
                  variant={"transparent"}
                  value={innerValue}
                  onChange={(...values) => {
                    setValue(values[0]);
                  }}
                  expandable={false}
                  // extensionConfig={{ lineNumbers: { enabled: true } }}
                />
              }
            />
          </ScrollArea>
          {description && <div {...getDescriptionProps()}>{description}</div>}
          {props.isInvalid && <div {...getMessageProps()}>{errorMessage}</div>}
        </div>
      </Popover>
    </CodeControlContext.Provider>
  );
};

export const CodeEditor = (props: CodeEditorProps) => {
  const defaultProps = useDefaultProps(props);
  return <CodeCommon {...defaultProps} />;
};
