/* eslint-disable @typescript-eslint/no-explicit-any */
import { autocompletion, CompletionSource } from "@codemirror/autocomplete";
import { useMemo } from "react";
import { ExposingCompletionSource } from "../completion/exposingCompletionSource";
import { TernServer } from "../completion/ternServer";
import { useCodeController } from "../components/context";
import { CodeEditorProps } from "../types";

export function useCompletionSources(props: CodeEditorProps) {
  const codeController = useCodeController();
  // javascript syntax auto-completion
  const exposingSource = useMemo(() => new ExposingCompletionSource(), []);
  const ternServer = useMemo(() => new TernServer(), []);

  useMemo(() => {
    ternServer.completionController = codeController;
    exposingSource.completionController = codeController;
  }, [codeController, exposingSource, ternServer]);

  const completionSources = useMemo(() => {
    const sources: CompletionSource[] = [];
    sources.push(exposingSource as any);
    if (codeController.enableJsServer && ternServer) {
      sources.push(ternServer as any);
    }
    return sources.map((c: any) => {
      return c.completionSource;
    });
  }, [codeController.enableJsServer, exposingSource, ternServer]);

  return completionSources;
}

export function useAutocompletionExtension(props: CodeEditorProps) {
  const completions = useCompletionSources(props);
  // return useMemo(() => [autocompletion()], []);
  return useMemo(
    () => [
      autocompletion({
        override: completions,
        // defaultKeymap: false,
      }),
      // keyMapExtensions,
    ],
    [completions],
  );
}
