import { useMemo } from "react";
import { CodeEditorProps } from "../types";

export const useDefaultProps = (props: CodeEditorProps) => {
  const { extensionConfig } = props;

  return useMemo(
    () =>
      ({
        placeholder: "Please input",
        bordered: props.editable !== false,
        codeType: "Function",
        popover: props.codeType === "Text",
        expandable: props.codeType === "Text",
        enableJsServer: props.codeType !== "Text",
        ...props,
        extensionConfig: {
          ...extensionConfig,
          lineNumbers: {
            enabled: props.codeType !== "Text",
            ...extensionConfig?.lineNumbers,
          },
        },
      }) as CodeEditorProps,
    [extensionConfig, props],
  );
};
