import {
  Decoration,
  MatchDecorator,
  DecorationSet,
  EditorView,
  ViewPlugin,
  ViewUpdate,
} from "@codemirror/view";

export const markKeyword = (
  regexp: RegExp,
  className: string,
  attribute?: { [key: string]: string },
) => {
  if (String(regexp) === "/(?:)/g") {
    return [];
  }
  const placeholderMatcher = new MatchDecorator({
    regexp,
    decoration: () => {
      return Decoration.mark({
        class: className,
        attributes: attribute,
      });
    },
  });

  return ViewPlugin.fromClass(
    class {
      placeholders: DecorationSet;
      constructor(view: EditorView) {
        this.placeholders = placeholderMatcher.createDeco(view);
      }
      update(update: ViewUpdate) {
        this.placeholders = placeholderMatcher.updateDeco(update, this.placeholders);
      }
    },
    {
      decorations: (instance) => instance.placeholders,
    },
  );
};
