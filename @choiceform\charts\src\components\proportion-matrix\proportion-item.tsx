import { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { ChartTooltip } from "../chart-tooltip";

export type ProportionData = {
  label: string;
  value: number[];
};

export type circleStyle = {
  fill?: string[];
  fillOpacity?: number[];
  stroke?: string[];
  strokeWidth?: number;
  strokeOpacity?: number;
};

type ProportionItemProps = {
  data: ProportionData;
  maxRadius: number;
  maxValues: number[];
  circleStyle?: circleStyle;
  animate?: boolean;
  duration?: number;
};

export const ProportionItem = ({
  data,
  maxRadius,
  maxValues,
  circleStyle,
  animate,
  duration = 1000,
}: ProportionItemProps) => {
  const ref = useRef(null);
  const [tooltip, setTooltip] = useState({ show: false, x: 0, y: 0, content: "" });

  useEffect(() => {
    const svg = d3.select(ref.current);

    // 清除之前的图形
    svg.selectAll("*").remove();

    // 计算比例尺
    const scales = maxValues.map((maxValue) =>
      d3.scaleSqrt().domain([0, maxValue]).range([0, maxRadius]),
    );

    // 计算圆形的半径
    const radii = data.value.map((value, i) => scales[i](value));

    // 计算 SVG 的尺寸
    const svgSize = Math.max(...radii) * 2;

    // 更新 SVG 的尺寸
    svg.attr("width", svgSize).attr("height", svgSize);

    let cumulativeDelay = 0;
    // 创建圆形，根据半径大小决定绘制顺序
    radii
      .map((r, originalIndex) => ({ r, originalIndex }))
      .sort((a, b) => b.r - a.r)
      .forEach(({ r, originalIndex }) =>
        svg
          .append("circle")
          .attr("cx", svgSize / 2)
          .attr("cy", svgSize / 2)
          .attr("r", animate ? 0 : r)
          .attr(
            "fill",
            circleStyle?.fill
              ? circleStyle?.fill[originalIndex % circleStyle?.fill.length]
              : d3.schemeCategory10[originalIndex % 10],
          )
          .attr(
            "fill-opacity",
            circleStyle?.fillOpacity
              ? circleStyle?.fillOpacity[originalIndex % circleStyle?.fillOpacity.length]
              : 1,
          )
          .attr(
            "stroke",
            circleStyle?.stroke
              ? circleStyle?.stroke[originalIndex % circleStyle?.stroke.length]
              : "none",
          )
          .attr("stroke-width", circleStyle?.strokeWidth ?? 0)
          .attr("stroke-opacity", circleStyle?.strokeOpacity ?? 1)
          .on("mouseover", function (event) {
            setTooltip({
              show: true,
              x: d3.pointer(event, this)[0],
              y: d3.pointer(event, this)[1] - 28,
              content: `Value: ${data.value[originalIndex]}`,
            });
          })
          .on("mouseout", function () {
            setTooltip({
              show: false,
              x: 0,
              y: 0,
              content: "",
            });
          })
          .transition()
          .duration(duration)
          .delay(cumulativeDelay + originalIndex * duration)
          .attrTween("r", function () {
            return function (t: number): string {
              return (t * r).toString();
            };
          }),
      );
    cumulativeDelay += duration;
  }, [
    animate,
    circleStyle?.fill,
    circleStyle?.fillOpacity,
    circleStyle?.stroke,
    circleStyle?.strokeOpacity,
    circleStyle?.strokeWidth,
    data,
    duration,
    maxRadius,
    maxValues,
  ]);

  return (
    <div
      style={{
        position: "relative",
        width: "100%",
        height: "100%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        aspectRatio: "1",
      }}
    >
      <svg
        style={{
          overflow: "visible",
        }}
        ref={ref}
      ></svg>
      {tooltip.show && (
        <ChartTooltip
          x={tooltip.x}
          y={tooltip.y}
        >
          {tooltip.content}
        </ChartTooltip>
      )}
    </div>
  );
};
