import type { ReactCodeMirrorProps, ReactCodeMirrorRef } from "@uiw/react-codemirror";
import ReactCodeMirror from "@uiw/react-codemirror";
import { forwardRef } from "react";
import { useExtensions } from "../../hooks/use-extensions";
import type { CodeEditorProps } from "../../types";

export const ChoiceformCodeMirror = forwardRef<ReactCodeMirrorRef, CodeEditorProps>(
  function CodeMirror(
    { basicSetup: _basicSetup, extensions: _extensions, extensionConfig, preview, ...props },
    ref,
  ) {
    const { basicSetup, extensions } = useExtensions({
      basicSetup: _basicSetup,
      extensions: _extensions,
      extensionConfig,
    });

    return (
      <ReactCodeMirror
        ref={ref}
        basicSetup={basicSetup}
        extensions={extensions}
        {...(props as ReactCodeMirrorProps)}
      />
    );
  },
);
