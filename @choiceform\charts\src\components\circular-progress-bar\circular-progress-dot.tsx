interface DotData {
  dot?: {
    position: 'start' | 'start-alien' | 'end' | 'end-alien' | 'center' | 'dynamic';
    duration?: number;
  };
  dotStyle?: {
    fill?: string;
    fillOpacity?: number;
    stroke?: string;
    strokeWidth?: number;
    size?: number;
  };
}

export interface ArcData {
  data: DotData;
  startAngle: number;
  endAngle: number;
}

export function appendDot(
  d: ArcData,
  g: d3.Selection<SVGGElement, unknown, null, undefined>,
  newInnerRadius: number,
  arcWidth: number,
  gap: number,
  animate?: boolean
) {
  // 计算 dot 的坐标
  const dotAngle = arcWidth / 2 / newInnerRadius;
  let correctedEndAngle;
  switch (d.data.dot?.position ?? 'end') {
    case 'start':
      correctedEndAngle = d.startAngle;
      break;
    case 'start-alien':
      correctedEndAngle = d.startAngle + dotAngle + gap / 2 / newInnerRadius;
      break;
    case 'end':
      correctedEndAngle = d.endAngle;
      break;
    case 'end-alien':
      correctedEndAngle = d.endAngle - dotAngle - gap / 2 / newInnerRadius;
      break;
    case 'center':
      correctedEndAngle = (d.startAngle + d.endAngle) / 2;
      break;
    default:
      correctedEndAngle = d.endAngle - dotAngle; // default to 'end' behavior
  }
  const x = (newInnerRadius + arcWidth / 2) * Math.cos(correctedEndAngle - Math.PI / 2);
  const y = (newInnerRadius + arcWidth / 2) * Math.sin(correctedEndAngle - Math.PI / 2);
  const dot = g
    .append('circle')
    .attr('cx', x)
    .attr('cy', y)
    .attr('r', 0) // 初始半径为0
    .attr('fill', d.data.dotStyle?.fill ?? 'none')
    .attr('fill-opacity', d.data.dotStyle?.fillOpacity ?? 1)
    .attr('stroke', d.data.dotStyle?.stroke ?? 'none')
    .attr('stroke-width', d.data.dotStyle?.strokeWidth ?? 0)
    .attr('opacity', animate ? 0 : 1);

  if (animate) {
    dot
      .transition() // 添加 dot 的动画
      .duration(d.data.dot?.duration ?? 500) // 动画持续时间
      .attrTween('r', function () {
        // 使用 attrTween 为 r 属性创建动画
        return function (t: number): string {
          // 返回一个介于 0 和 arcWidth/2 之间的值
          return (t * (d.data.dotStyle?.size ?? arcWidth / 2)).toString();
        };
      })
      .style('opacity', 1);
  } else {
    dot.attr('r', d.data.dotStyle?.size ?? arcWidth / 2).style('opacity', 1);
  }
}
