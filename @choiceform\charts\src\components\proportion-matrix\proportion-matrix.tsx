import { ProportionItem, type circleStyle } from "./proportion-item";

export type ProportionData = {
  label: string;
  value: number[];
};

type ProportionMatrixProps = {
  data: ProportionData[];
  maxRadius?: number;
  circleStyle?: circleStyle;
  animate?: boolean;
  duration?: number;
};

export const ProportionMatrix = ({
  data,
  maxRadius = 100,
  circleStyle,
  animate,
  duration = 1000,
}: ProportionMatrixProps) => {
  // 计算每个值的最大值
  const maxValues =
    data.length > 0
      ? data[0].value.map((_, i) => Math.max(...data.map((item) => item.value[i])))
      : [];

  return (
    <div
      className="grid gap-8"
      style={{
        gridTemplateColumns: `repeat(auto-fit, minmax(${maxRadius * 2}px, 1fr))`,
      }}
    >
      {data.map((itemData, index) => {
        return (
          <div
            className="flex flex-col items-center justify-center gap-4 font-sans text-lg"
            key={index}
          >
            <ProportionItem
              data={itemData}
              circleStyle={circleStyle}
              maxRadius={maxRadius}
              maxValues={maxValues}
              animate={animate}
              duration={duration}
            />
            <div
              style={{
                fontSize: "1rem",
              }}
            >
              {itemData.label}
            </div>
          </div>
        );
      })}
    </div>
  );
};
