import {
  <PERSON><PERSON>,
  <PERSON>rollA<PERSON>,
  SvgIcon,
  SvgIconName,
  Tooltip,
  tcx,
  useMergedValue,
  usePopoverEmptyInstance,
} from "@choiceform/ui-react";
import { defaultKeymap, history, historyKeymap } from "@codemirror/commands";
import { defaultHighlightStyle, syntaxHighlighting } from "@codemirror/language";
import { highlightSelectionMatches } from "@codemirror/search";
import { EditorView, keymap } from "@codemirror/view";
import ReactCodeMirror, { ReactCodeMirrorRef } from "@uiw/react-codemirror";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { obj2CodeInputString, string2CodeInputObj } from "../../completion/utils";
import { defaultTheme } from "../../hooks/use-custom-style";
import { markKeyword } from "../../plugin/placeholder-keyword";
import { placeholders } from "../../plugin/placeholder-matcher";
import { CodeInputProps } from "../../types";
import { useFormulaCompletion } from "../FormulaEditor/formulaCompletion";
import { useCodeCore } from "../use-code";
import { CodeInputVariant } from "../code.tv";

export function CodeInput<T>(props: CodeInputProps<T>) {
  const {
    activeFocus,
    classNames,
    contentRender,
    defaultValue,
    isDisabled,
    isInvalid,
    isReadOnly,
    disableFocus,
    formulaData,
    intent,
    isTriggerChangeOnInit = false,
    onChange,
    onInit,
    onSourceChange,
    popoverProps,
    quoteRender,
    readOnly,
    setSize,
    showRefSelect = true,
    leftNode,
    striking,
    triggerRender,
    triggerTooltip = "Add dynamic field",
    value,
    variant,
    indicatorIcon = <SvgIcon name={SvgIconName.datasheet.fieldDynamic} />,
    ...rest
  } = props;
  // const isControlled = value !== undefined;
  const [open, setOpen] = useState(false);

  const {
    label,
    description,
    errorMessage,
    getBaseProps,
    getScrollAreaProps,
    getLabelProps,
    getDescriptionProps,
    getMessageProps,
  } = useCodeCore({
    ...rest,
    classNames,
    editorType: "input",
    setSize,
    isDisabled,
    isReadOnly,
    isInvalid,
    variant,
    intent,
    activeFocus: open,
  });

  const [innerValue, setValue] = useMergedValue({
    value: obj2CodeInputString(value),
    defaultValue: obj2CodeInputString(defaultValue),
    onChange: (value) => {
      onSourceChange?.(value);
      onChange?.(string2CodeInputObj(value));
    },
  });

  // const changeFilterExtension = useChangeFilterExtension(value !== undefined, innerValue, setValue);
  const ref = useRef<ReactCodeMirrorRef>({});
  const instance = usePopoverEmptyInstance();

  const apply = useCallback((value: T) => {
    if (ref.current) {
      let insert = value as string;
      if (typeof value === "object") {
        insert = JSON.stringify(value);
      }
      insert = `\${{${insert}}}$`;
      const { view } = ref.current;
      if (view) {
        view.focus();
        const from = view.state.selection.main.head;
        view.dispatch({
          changes: {
            from,
            to: from,
            insert,
          },
          selection: {
            anchor: from + insert.length,
            head: from + insert.length,
          },
        });
      }
    }
  }, []);

  useEffect(() => {
    onInit?.({ setValue });
    if (isTriggerChangeOnInit) {
      onSourceChange?.(innerValue);
      onChange?.(string2CodeInputObj(innerValue));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const quote = useRef(quoteRender);
  quote.current = quoteRender;

  const placeholdersExtension = useMemo(() => {
    return placeholders(quote);
  }, []);

  // 公式提示
  const formulaCompletion = useFormulaCompletion({
    formulaData: [],
    fieldData: [],
    ...props,
  });

  // 公式高亮
  const highlightFormula = useMemo(() => {
    const keywords = formulaData?.filter((f) => f.type === "keyword").map((f) => f.name) || [];
    const functions = formulaData?.filter((f) => f.type === "function").map((f) => f.name) || [];
    return [
      markKeyword(new RegExp(keywords.join("|"), "g"), "cm-keyword"),
      markKeyword(new RegExp(functions.join("|"), "g"), "cm-function"),
    ];
  }, [formulaData]);

  const formulaExtensions = useMemo(() => {
    if (!formulaData) return [];
    return [
      formulaCompletion,
      highlightFormula,
      syntaxHighlighting(defaultHighlightStyle, { fallback: true }),
      highlightSelectionMatches(),
    ];
  }, [formulaData, formulaCompletion, highlightFormula]);

  const extensions = useMemo(
    () => [
      formulaExtensions,
      defaultTheme,
      placeholdersExtension,
      EditorView.lineWrapping,
      history(),
      keymap.of([...defaultKeymap, ...historyKeymap]),
    ],
    [formulaExtensions, placeholdersExtension],
  );

  const onChangeFn = useCallback(
    (value: string) => {
      setValue(value);
    },
    [setValue],
  );

  const slots = useMemo(
    () =>
      CodeInputVariant({
        isOpen: open,
        setSize,
        isDisabled,
      }),
    [open, setSize, isDisabled],
  );

  const scrollAreaProps = useMemo(() => {
    return getScrollAreaProps();
  }, [getScrollAreaProps]);

  return (
    <Popover
      matchTriggerWidth
      trigger="none"
      instance={instance}
      placement="bottom"
      content={contentRender?.(instance, apply)}
      onOpenChange={setOpen}
      {...popoverProps}
    >
      <div {...getBaseProps()}>
        {label && <label {...getLabelProps()}>{label}</label>}
        <ScrollArea
          classNames={{
            content: tcx(
              scrollAreaProps.classNames.content,
              isDisabled && "cursor-not-allowed bg-light-200 relative",
            ),
          }}
          {...scrollAreaProps}
        >
          <ReactCodeMirror
            data-slot="codeMirror"
            // className={slots.codeMirror({ class: classNames?.codeMirror })}
            className={slots.codeMirror()}
            basicSetup={false}
            {...rest}
            ref={ref}
            extensions={extensions}
            value={innerValue}
            onChange={onChangeFn}
            readOnly={isDisabled || isReadOnly}
          />
          {isDisabled && <div className="absolute top-0 left-0 w-full h-full"></div>}
          {triggerRender ? (
            triggerRender(instance)
          ) : (
            <>
              {leftNode && <>{leftNode}</>}
              {showRefSelect && (
                <div
                  data-slot="indicator"
                  className={slots.indicator()}
                >
                  <div
                    data-slot="indicatorContainer"
                    className={slots.indicatorContainer()}
                  >
                    <Tooltip
                      isDisabled={isDisabled}
                      content={triggerTooltip}
                    >
                      <div
                        role="button"
                        data-disabled={isDisabled}
                        data-slot="indicatorButton"
                        className={slots.indicatorButton()}
                        onClick={() => !isDisabled && instance.open()}
                      >
                        {typeof indicatorIcon === "string" ? (
                          <div
                            data-slot="indicatorIcon"
                            className={tcx(slots.indicatorIcon(), indicatorIcon)}
                          />
                        ) : (
                          indicatorIcon
                        )}
                      </div>
                    </Tooltip>
                  </div>
                </div>
              )}
            </>
          )}
        </ScrollArea>
        {description && <div {...getDescriptionProps()}>{description}</div>}
        {props.isInvalid && <div {...getMessageProps()}>{errorMessage}</div>}
      </div>
    </Popover>
  );
}
