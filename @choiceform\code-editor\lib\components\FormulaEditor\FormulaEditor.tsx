import { Expandable, Scroll<PERSON>rea, useMergedValue } from "@choiceform/ui-react";
import { closeBrackets, closeBracketsKeymap, completionKeymap } from "@codemirror/autocomplete";
import { defaultKeymap, history, historyKeymap } from "@codemirror/commands";
import {
  bracketMatching,
  defaultHighlightStyle,
  indentOnInput,
  syntaxHighlighting,
} from "@codemirror/language";
import { highlightSelectionMatches } from "@codemirror/search";
import { EditorState } from "@codemirror/state";
import { EditorView, dropCursor, keymap } from "@codemirror/view";
import ReactCodeMirror, { ReactCodeMirrorRef } from "@uiw/react-codemirror";
import { useMemo, useRef } from "react";
import { obj2CodeInputString, string2CodeInputObj } from "../../completion/utils";
import { defaultTheme } from "../../hooks/use-custom-style";
import { markKeyword } from "../../plugin/placeholder-keyword";
import { placeholders } from "../../plugin/placeholder-matcher";
import { FormulaEditorProps } from "../../types";
import { CodeInputVariant } from "../code.tv";
import { useCodeCore } from "../use-code";
import { useFormulaCompletion } from "./formulaCompletion";

export function FormulaEditor(props: FormulaEditorProps) {
  const {
    activeFocus,
    classNames,
    defaultValue,
    isDisabled,
    isInvalid,
    isReadOnly,
    expandableProps,
    expandable = false,
    expandedTitle,
    fieldData,
    disableFocus,
    formulaData,
    intent,
    onChange,
    quoteRender,
    readOnly,
    scrollAreaProps,
    setSize = "md",
    striking,
    value,
    variant,
    ...rest
  } = props;
  // const isControlled = value !== undefined;

  const {
    label,
    description,
    errorMessage,
    getBaseProps,
    getScrollAreaProps,
    getLabelProps,
    getDescriptionProps,
    getMessageProps,
  } = useCodeCore({
    classNames,
    editorType: "formula",
    setSize,
    isDisabled,
    isReadOnly,
    isInvalid,
    variant,
    intent,
  });

  const [innerValue, setValue] = useMergedValue({
    value: obj2CodeInputString(value),
    defaultValue: obj2CodeInputString(defaultValue),
    onChange: (value) => {
      onChange?.(string2CodeInputObj(value), value);
    },
  });
  // const changeFilterExtension = useChangeFilterExtension(value !== undefined, innerValue, setValue);
  const ref = useRef<ReactCodeMirrorRef>({});

  const formulaCompletion = useFormulaCompletion(props);

  const highlightFormula = useMemo(() => {
    const keywords = formulaData.filter((f) => f.type === "keyword").map((f) => f.name);
    const functions = formulaData.filter((f) => f.type === "function").map((f) => f.name);
    const fields = fieldData.map((f) => f.name);
    // console.log(new RegExp('', 'g'));
    return [
      markKeyword(new RegExp(keywords.join("|"), "g"), "cm-keyword"),
      markKeyword(new RegExp(functions.join("|"), "g"), "cm-function"),
      markKeyword(new RegExp(fields.join("|"), "g"), "cm-field"),
    ];
  }, [fieldData, formulaData]);

  const baseExtension = useMemo(
    () => [
      syntaxHighlighting(defaultHighlightStyle, { fallback: true }),
      highlightSelectionMatches(),
      // highlightActiveLine(),
      defaultTheme,
      indentOnInput(),
      bracketMatching(),
      // rectangularSelection(),
      EditorView.lineWrapping,
      EditorState.allowMultipleSelections.of(true),
      // drawSelection(),
      dropCursor(),
      history(),
      closeBrackets(),
      keymap.of([...closeBracketsKeymap, ...defaultKeymap, ...historyKeymap, ...completionKeymap]),
    ],
    [],
  );
  const quote = useRef(quoteRender);
  quote.current = quoteRender;
  const placeholdersExtension = useMemo(() => {
    return placeholders(quote);
  }, []);

  const extensions = useMemo(
    () => [baseExtension, placeholdersExtension, formulaCompletion, highlightFormula],
    [baseExtension, formulaCompletion, highlightFormula, placeholdersExtension],
  );

  const slots = useMemo(
    () =>
      CodeInputVariant({
        setSize,
      }),
    [setSize],
  );

  return (
    <div {...getBaseProps()}>
      {label && <label {...getLabelProps()}>{label}</label>}
      <ScrollArea {...getScrollAreaProps()}>
        <Expandable
          title={expandedTitle}
          setSize={setSize}
          expandable={expandable}
          hoverVisiable
          className="w-full"
          {...expandableProps}
          collapsed={
            <ReactCodeMirror
              data-slot="codeMirror"
              // className={slots.codeMirror({ class: classNames?.codeMirror })}
              className={slots.codeMirror()}
              {...rest}
              value={innerValue}
              basicSetup={false}
              ref={ref}
              onChange={setValue}
              extensions={extensions}
            />
          }
          expanded={
            <FormulaEditor
              {...props}
              className="flex-grow h-full w-full"
              scrollAreaProps={{
                classNames: {
                  viewport: "h-auto",
                },
              }}
              minWidth="100%"
              variant="transparent"
              value={string2CodeInputObj(innerValue)}
              onChange={(_, value) => {
                setValue(value);
              }}
              expandable={false}
            />
          }
        />
      </ScrollArea>
      {description && <div {...getDescriptionProps()}>{description}</div>}
      {props.isInvalid && <div {...getMessageProps()}>{errorMessage}</div>}
    </div>
  );
}
