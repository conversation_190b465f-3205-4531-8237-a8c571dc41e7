import { HighlightStyle } from "@codemirror/language";
import { EditorView } from "@codemirror/view";

// export function useCustomStyle(styles: CSSProperties, props: CodeEditorProps) {
//   const stylesContainer = createElementWithId('cf-code-editor-style', 'style');
//   stylesContainer.textContent = stylesToString(styles, props);
//   document.head.appendChild(stylesContainer);
// }

function createElementWithId(id: string, tagName = "div") {
  if (id.indexOf("#") === 0) {
    id = id.split("#")[1];
  }

  const target = document.querySelector(`#${id}`);

  if (target instanceof Element) {
    return target;
  }

  const element = document.createElement(tagName);
  element.setAttribute("id", id);
  return element;
}

// function stylesToString(styles: CSSProperties, props: CodeEditorProps) {
//   let finale = ``;

//   for (const [property, value] of Object.entries(styles)) {
//     if (property === 'outline') {
//       finale += `.cf-code-editor .cm-editor.cm-focused { ${property}: ${value}; }`;
//     }
//   }

//   return finale;
// }

const textStyle = {
  "font-family": "var(--font-en-us)",
  "word-wrap": "break-word",
  "word-break": "break-all",
  "white-space": "pre-wrap",
  "font-size": "14px",
  "line-height": "14px",
};

// export const choiceformCodeEditorTooltip = css`
//   position: relative;
//   height: 100%;

//   .cm-tooltip {
//     z-index: 910;

//     &.cm-tooltip-autocomplete {
//       border: none;

//       > ul {
//         background-color: white;
//         border: 1px solid #708090;
//         box-shadow: 0 2px 16px rgba(0, 0, 0, 0.16);
//         border-radius: 8px;
//         font-family: "Fira Code", monospace;

//         li {
//           height: 24px;
//           line-height: 24px;
//           position: relative;
//           overflow: hidden;

//           &[aria-selected] {
//             background-color: #66b;
//             color: #66b;
//           }

//           .cm-completionIcon {
//             width: 14px;
//             height: 14px;
//             font-size: 14px;
//             opacity: 1;
//             padding-right: 8px;
//             position: absolute;
//             top: 2.5px;
//           }
//           .cm-completionLabel {
//             font-size: 12px;
//             line-height: 22px;
//             position: absolute;
//             left: 25px;
//             top: 2px;

//             .cm-completionMatchedText {
//               font-weight: 600;
//               text-decoration: none;
//             }
//           }

//           .cm-completionDetail {
//             position: absolute;
//             right: 8px;
//             top: 2px;
//             margin: 0;
//             color: #708090;
//             font-size: 12px;
//             line-height: 22px;
//             font-style: normal;
//           }
//         }
//       }

//       .cm-completionInfo {
//         padding: 4px 8px;
//         background-color: white;
//         border: 1px solid #708090;
//         box-shadow: 0 2px 16px rgba(0, 0, 0, 0.16);
//         border-radius: 8px;
//         width: 287px;

//         &.cm-completionInfo-right {
//           left: calc(100% + 8px);
//         }

//         .completionInfoCardTitle {
//           display: flex;
//           justify-content: space-between;
//           align-items: center;

//           .cardTitle {
//             width: 100%;
//             font-weight: 500;
//             font-size: 12px;
//             line-height: 20px;
//             color: #66b;
//           }

//           .openInfo {
//             width: 12px;
//             height: 12px;
//             display: flex;
//             align-items: center;
//           }
//         }

//         .completionInfoType {
//           font-size: 12px;
//           color: #708090;
//           margin: 0;
//           line-height: 20px;
//         }

//         .completionInfoEvaluatesTitle {
//           font-size: 12px;
//           color: #708090;
//           margin: 0;
//           font-weight: 500;
//           line-height: 20px;
//         }

//         .completionInfoDoc {
//           font-size: 12px;
//           color: #708090;
//           margin: 0;
//           line-height: 20px;
//         }

//         .evaluatesResult {
//           display: inline-block;
//           margin: 0;
//           padding: 0 8px;
//           font-size: 12px;
//           line-height: 18px;
//           color: #708090;
//           background-color: #708090;
//           position: relative;
//           cursor: pointer;

//           :hover {
//             .evaluatesTooltips {
//               visibility: visible;
//             }
//           }

//           .evaluatesTooltips {
//             visibility: hidden;
//             font-family: "Fira Code", monospace;
//             position: absolute;
//             left: calc(100% + 4px);
//             top: -50%;
//             max-height: 162px;
//             border-radius: 4px;
//             box-shadow: 0 2px 16px rgba(0, 0, 0, 0.16);
//             background-color: #708090;
//             padding: 12px 16px;
//             font-size: 14px;
//             line-height: 18px;
//             color: white;
//             white-space: pre;
//             overflow-y: auto;
//             cursor: auto;
//           }
//         }
//       }
//     }
//   }
// `;

export const defaultTheme = EditorView.baseTheme({
  // '.cm-content, .cm-gutter': {
  //   height: '100%',
  // },
  // '.cm-default-quote': {
  //   paddingLeft: '6px',
  //   paddingRight: '6px',
  //   paddingTop: '3px',
  //   paddingBottom: '3px',
  //   marginLeft: '3px',
  //   marginRight: '3px',
  //   backgroundColor: '#ffcdcc',
  //   borderRadius: '4px',
  // },
  // '.cm-function': {
  //   color: '#9d8911',
  // },
  // '.cm-keyword': {
  //   color: '#f47067',
  // },
  // '.cm-tooltip.cm-tooltip-cursor': {
  //   backgroundColor: '#66b',
  //   color: 'white',
  //   border: 'none',
  //   padding: '2px 7px',
  //   borderRadius: '4px',
  //   '&.cm-tooltip-arrow:before': {
  //     borderTopColor: '#66b',
  //   },
  //   '&.cm-tooltip-arrow:after': {
  //     borderTopColor: 'transparent',
  //   },
  // },
  // '&.cm-editor': {
  //   backgroundColor: 'unset',
  //   // 'font-size': '14px',
  //   outline: 'none',
  //   borderRadius: '0 !important',
  //   // 'min-height': '30px',
  //   border: 'none !important',
  // },
  // '& .cm-line': {
  //   padding: '0 !important',
  // },
  // // '& .cm-line >.cm-widgetBuffer:first-child': {
  // //   'margin-left': '-6px',
  // // },
  // '.cm-scroller': {
  //   overflow: 'unset !important',
  //   lineHeight: '22px',
  //   fontFamily: 'var(--font-en-us)',
  // },
  // '.cm-scroller::-webkit-scrollbar': {
  //   width: '16px',
  // },
  // '.cm-content': {
  //   //'border-radius': '5px',
  //   padding: '0 !important',
  // },
  // '.cm-scroller::-webkit-scrollbar-thumb': {
  //   border: '5px solid transparent',
  //   backgroundClip: 'content-box',
  //   borderRadius: '9999px',
  //   backgroundColor: 'rgba(139, 143, 163, 0.2)',
  //   minHeight: '30px',
  // },
  // '.cm-scroller::-webkit-scrollbar-thumb:hover': {
  //   backgroundColor: 'rgba(139, 143, 163, 0.5)',
  // },
  // '&.cm-editor:hover': {
  //   transition: 'all .4s ease',
  //   outline: 'none',
  // },
  // '&.cm-editor.cm-focused': {
  //   outline: 'none',
  // },
  // // matching brackets
  // '&.cm-editor .cm-scroller .cm-content[contenteditable=true] .cm-line .cm-matchingBracket': {
  //   color: '#40A072',
  //   backgroundColor: '#e9f4e6',
  // },
  // // line number
  // '& .cm-gutters': {
  //   flexShrink: 0,
  //   width: '40px',
  //   backgroundColor: 'var(--light-100) !important',
  //   color: '#8B8FA3',
  //   borderRadius: '4px 0px 0px 4px',
  //   borderRight: 'none',
  //   padding: '0 2px 0 4px',
  //   userSelect: 'none',
  // },
  // '& .cm-gutters + .cm-content': { padding: '.5rem .75rem !important' },
  // '& .cm-lineNumbers': {
  //   flexGrow: 1,
  // },
  // '& .cm-lineNumbers .cm-gutterElement': {
  //   minWidth: 'unset',
  //   width: '100%',
  //   textAlign: 'center',
  //   padding: '0',
  //   ...textStyle,
  //   lineHeight: '22px',
  // },
  // '&  .cm-activeLineGutter': {
  //   backgroundColor: 'unset',
  //   color: '#222222',
  // },
  // '& .cm-lineNumbers .cm-activeLineGutter': {
  //   ...textStyle,
  //   lineHeight: '22px',
  //   fontWeight: 500,
  // },
  // '.cm-placeholder': {
  //   height: 0,
  // },
  // // highlight
  // '.cm-success-highlight': { background: '#EFF9F6' },
  // '.cm-error-highlight': { background: '#FFF3F1' },
});

const colors = {
  text: "#adbac7",
  bg: "white",
  guttersBg: "#22272e",
  guttermarkerText: "#22272e",
  guttermarkerSubtleText: "#636e7b",
  linenumberText: "#768390",
  cursor: "#cdd9e5",
  selectionBg: "rgba(108,182,255,0.3)",
  activelineBg: "#2d333b",
  matchingbracketText: "#adbac7",
  linesBg: "#22272e",
  syntax: {
    comment: "#768390",
    constant: "#6cb6ff",
    entity: "#dcbdfb",
    keyword: "#f47067",
    storage: "#f47067",
    string: "#96d0ff",
    support: "#6cb6ff",
    variable: "#f69d50",
  },
};

// https://github.com/lezer-parser/javascript/blob/main/src/highlight.js
export const myHighlightStyle = HighlightStyle.define([
  // {
  //   tag: [tags.variableName, tags.propertyName, tags.derefOperator, tags.separator],
  //   color: 'white',
  // },
  // {
  //   tag: [tags.comment, tags.lineComment, tags.blockComment],
  //   color: theme.scale.gray[3],
  // },
  // {
  //   tag: [
  //     tags.definitionKeyword,
  //     tags.bitwiseOperator,
  //     tags.logicOperator,
  //     tags.arithmeticOperator,
  //     tags.definitionOperator,
  //     tags.updateOperator,
  //     tags.compareOperator,
  //     tags.operatorKeyword,
  //     tags.punctuation,
  //     tags.null,
  //     tags.keyword,
  //   ],
  //   color: colors.syntax.keyword,
  // },
  // {
  //   tag: [tags.string, tags.special(tags.string)],
  //   color: colors.syntax.string,
  // },
  // {
  //   tag: [tags.regexp],
  //   color: theme.scale.orange[2],
  // },
  // { tag: [tags.self], color: theme.scale.blue[3] },
  // {
  //   tag: [tags.number, tags.bool, tags.modifier, tags.atom],
  //   color: theme.scale.blue[2],
  // },
  // {
  //   tag: [
  //     tags.special(tags.variableName),
  //     tags.function(tags.variableName),
  //     tags.function(tags.propertyName),
  //     tags.typeName,
  //     tags.labelName,
  //     tags.className,
  //   ],
  //   color: '#9d8911',
  // },
  // { tag: [tags.bracket], color: theme.scale.yellow[1] },
]);
